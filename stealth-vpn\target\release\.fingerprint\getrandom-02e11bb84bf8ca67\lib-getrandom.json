{"rustc": 16591470773350601817, "features": "[\"std\"]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 16244099637825074703, "profile": 17665183640080672258, "path": 16101640464166662954, "deps": [[10411997081178400487, "cfg_if", false, 13646519999268015485]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\getrandom-02e11bb84bf8ca67\\dep-lib-getrandom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
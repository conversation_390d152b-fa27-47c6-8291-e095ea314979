//! Enhanced License Key Generator with Advanced Features

use clap::Parser;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use std::time::{SystemTime, UNIX_EPOCH};
use blake3::Hasher;
use rand::{RngCore, rngs::OsRng};

#[derive(Parser)]
struct Args {
    #[arg(short, long, default_value = "demo-user")]
    user_id: String,
    
    #[arg(short, long, default_value = "premium")]
    tier: String,
    
    #[arg(short, long, default_value = "30")]
    days: u64,
    
    #[arg(short = 'H', long)]
    hardware: Option<String>,
    
    #[arg(short, long, default_value = "1")]
    quantity: u32,
    
    #[arg(short, long)]
    batch_name: Option<String>,
    
    #[arg(long)]
    export_csv: bool,
    
    #[arg(long)]
    quantum_resistant: bool,
}

#[derive(Serialize, Deserialize)]
struct EnhancedLicenseData {
    // Core license info
    license_id: String,
    user_id: String,
    tier: String,
    hardware_fingerprint: String,
    expires_at: u64,
    issued_at: u64,
    
    // Enhanced features
    max_connections: u32,
    bandwidth_limit: u64,
    data_limit: u64,
    features: Vec<String>,
    allowed_regions: Vec<String>,
    
    // Security features
    quantum_resistant: bool,
    encryption_level: String,
    obfuscation_modes: Vec<String>,
    
    // Advanced features
    priority_level: u32,
    api_access: bool,
    custom_dns: bool,
    kill_switch: bool,
    split_tunneling: bool,
    
    // Metadata
    batch_id: Option<String>,
    issuer: String,
    signature: String,
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let args = Args::parse();
    
    print_enhanced_banner();
    
    println!("🎫 Enhanced UltraSecure VPN License Generator");
    println!("🔒 Security Level: QUANTUM-RESISTANT");
    println!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    
    let batch_id = args.batch_name.clone().or_else(|| {
        if args.quantity > 1 {
            Some(format!("batch_{}", Uuid::new_v4().to_string()[..8].to_uppercase()))
        } else {
            None
        }
    });
    
    let mut licenses = Vec::new();
    
    for i in 0..args.quantity {
        let license = generate_enhanced_license(&args, i, &batch_id)?;
        licenses.push(license);
    }
    
    if args.export_csv {
        export_to_csv(&licenses, &batch_id)?;
    } else {
        display_licenses(&licenses)?;
    }
    
    println!("\n🚀 Enhanced licenses generated successfully!");
    println!("💡 Deploy to your $5/month VPS for ultra-secure VPN service!");
    
    Ok(())
}

fn generate_enhanced_license(args: &Args, index: u32, batch_id: &Option<String>) -> Result<EnhancedLicenseData, Box<dyn std::error::Error>> {
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)?
        .as_secs();
    
    let expires_at = now + (args.days * 24 * 60 * 60);
    
    let hardware_fingerprint = args.hardware.clone().unwrap_or_else(|| {
        if args.quantity > 1 {
            format!("hardware_{:04}", index + 1)
        } else {
            get_current_hardware_fingerprint()
        }
    });
    
    let (max_connections, bandwidth_limit, data_limit, features, priority_level) = match args.tier.as_str() {
        "basic" => (
            1, 
            10_000_000,      // 10 MB/s
            10_000_000_000,  // 10 GB/month
            vec!["encryption".to_string(), "basic_obfuscation".to_string()],
            1
        ),
        "premium" => (
            3, 
            100_000_000,     // 100 MB/s
            100_000_000_000, // 100 GB/month
            vec![
                "encryption".to_string(), 
                "advanced_obfuscation".to_string(),
                "kill_switch".to_string(),
                "custom_dns".to_string(),
                "priority_support".to_string()
            ],
            2
        ),
        "enterprise" => (
            10, 
            0,  // Unlimited
            0,  // Unlimited
            vec![
                "encryption".to_string(), 
                "quantum_resistant".to_string(),
                "advanced_obfuscation".to_string(),
                "steganographic".to_string(),
                "kill_switch".to_string(),
                "split_tunneling".to_string(),
                "custom_dns".to_string(),
                "api_access".to_string(),
                "priority_support".to_string(),
                "dedicated_ip".to_string()
            ],
            3
        ),
        "developer" => (
            5,
            50_000_000,      // 50 MB/s
            50_000_000_000,  // 50 GB/month
            vec![
                "encryption".to_string(),
                "advanced_obfuscation".to_string(),
                "api_access".to_string(),
                "custom_dns".to_string(),
                "debugging_tools".to_string()
            ],
            2
        ),
        _ => (1, 10_000_000, 10_000_000_000, vec!["encryption".to_string()], 1),
    };
    
    let obfuscation_modes = if args.quantum_resistant || args.tier == "enterprise" {
        vec![
            "https_like".to_string(),
            "http2_like".to_string(),
            "webrtc_like".to_string(),
            "dns_tunneling".to_string(),
            "steganographic".to_string(),
            "adaptive".to_string()
        ]
    } else if args.tier == "premium" {
        vec![
            "https_like".to_string(),
            "http2_like".to_string(),
            "adaptive".to_string()
        ]
    } else {
        vec!["https_like".to_string()]
    };
    
    let license_data = EnhancedLicenseData {
        license_id: Uuid::new_v4().to_string(),
        user_id: if args.quantity > 1 {
            format!("{}_{:04}", args.user_id, index + 1)
        } else {
            args.user_id.clone()
        },
        tier: args.tier.clone(),
        hardware_fingerprint,
        expires_at,
        issued_at: now,
        max_connections,
        bandwidth_limit,
        data_limit,
        features: features.clone(),
        allowed_regions: vec![], // All regions allowed by default
        quantum_resistant: args.quantum_resistant || args.tier == "enterprise",
        encryption_level: if args.quantum_resistant || args.tier == "enterprise" {
            "QUANTUM_RESISTANT".to_string()
        } else {
            "MILITARY_GRADE".to_string()
        },
        obfuscation_modes,
        priority_level,
        api_access: features.contains(&"api_access".to_string()),
        custom_dns: features.contains(&"custom_dns".to_string()),
        kill_switch: features.contains(&"kill_switch".to_string()),
        split_tunneling: features.contains(&"split_tunneling".to_string()),
        batch_id: batch_id.clone(),
        issuer: "UltraSecure VPN Authority".to_string(),
        signature: generate_license_signature(&args.user_id, &expires_at.to_string())?,
    };
    
    Ok(license_data)
}

fn generate_license_signature(user_id: &str, expires_at: &str) -> Result<String, Box<dyn std::error::Error>> {
    let mut hasher = Hasher::new();
    hasher.update(user_id.as_bytes());
    hasher.update(expires_at.as_bytes());
    hasher.update(b"UltraSecure_VPN_Secret_Key"); // In production, use proper signing
    
    let hash = hasher.finalize();
    Ok(hex::encode(hash.as_bytes()))
}

fn get_current_hardware_fingerprint() -> String {
    let mut hasher = Hasher::new();
    
    // Add some system-specific data
    if let Ok(hostname) = std::env::var("COMPUTERNAME").or_else(|_| std::env::var("HOSTNAME")) {
        hasher.update(hostname.as_bytes());
    }
    
    if let Ok(user) = std::env::var("USERNAME").or_else(|_| std::env::var("USER")) {
        hasher.update(user.as_bytes());
    }
    
    // Add some randomness for demo purposes
    let mut random_bytes = [0u8; 16];
    OsRng.fill_bytes(&mut random_bytes);
    hasher.update(&random_bytes);
    
    let hash = hasher.finalize();
    hex::encode(&hash.as_bytes()[..16])
}

fn display_licenses(licenses: &[EnhancedLicenseData]) -> Result<(), Box<dyn std::error::Error>> {
    for (i, license) in licenses.iter().enumerate() {
        if i > 0 {
            println!("\n" + &"─".repeat(80));
        }
        
        println!("\n✅ Enhanced License #{} Generated:", i + 1);
        println!();
        println!("📋 License Details:");
        println!("   🆔 License ID: {}", license.license_id);
        println!("   👤 User ID: {}", license.user_id);
        println!("   🏷️  Tier: {} (Priority Level {})", license.tier.to_uppercase(), license.priority_level);
        println!("   ⏰ Valid for: {} days", (license.expires_at - license.issued_at) / 86400);
        println!("   🖥️  Hardware: {}...", &license.hardware_fingerprint[..16]);
        println!("   🔗 Max Connections: {}", license.max_connections);
        println!("   📊 Bandwidth: {} MB/s", if license.bandwidth_limit == 0 { "Unlimited".to_string() } else { (license.bandwidth_limit / 1_000_000).to_string() });
        println!("   💾 Data Limit: {} GB/month", if license.data_limit == 0 { "Unlimited".to_string() } else { (license.data_limit / 1_000_000_000).to_string() });
        println!("   🔒 Encryption: {}", license.encryption_level);
        
        if let Some(batch_id) = &license.batch_id {
            println!("   📦 Batch ID: {}", batch_id);
        }
        
        println!();
        println!("🎯 Features Enabled:");
        for feature in &license.features {
            println!("   ✅ {}", feature.replace("_", " ").to_uppercase());
        }
        
        println!();
        println!("🥷 Obfuscation Modes:");
        for mode in &license.obfuscation_modes {
            println!("   🔧 {}", mode.replace("_", " ").to_uppercase());
        }
        
        // Create license token
        let license_json = serde_json::to_string(&license)?;
        let license_token = base64::encode(&license_json);
        
        println!();
        println!("🔑 Enhanced License Token:");
        println!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        println!("{}", license_token);
        println!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    }
    
    if licenses.len() == 1 {
        let license_token = base64::encode(&serde_json::to_string(&licenses[0])?);
        println!();
        println!("💡 Usage Examples:");
        println!("   Server: cargo run --bin server");
        println!("   Client: cargo run --bin client -- --server 127.0.0.1:51820 --license \"{}\"", &license_token[..50]);
        println!("   Admin:  http://127.0.0.1:8080");
    }
    
    Ok(())
}

fn export_to_csv(licenses: &[EnhancedLicenseData], batch_id: &Option<String>) -> Result<(), Box<dyn std::error::Error>> {
    let filename = if let Some(batch_id) = batch_id {
        format!("licenses_{}.csv", batch_id)
    } else {
        format!("licenses_{}.csv", chrono::Utc::now().format("%Y%m%d_%H%M%S"))
    };
    
    let mut csv_content = String::new();
    csv_content.push_str("License ID,User ID,Tier,Hardware Fingerprint,Expires At,Max Connections,Bandwidth Limit,Data Limit,Features,License Token\n");
    
    for license in licenses {
        let license_json = serde_json::to_string(&license)?;
        let license_token = base64::encode(&license_json);
        
        csv_content.push_str(&format!(
            "{},{},{},{},{},{},{},{},\"{}\",\"{}\"\n",
            license.license_id,
            license.user_id,
            license.tier,
            license.hardware_fingerprint,
            license.expires_at,
            license.max_connections,
            license.bandwidth_limit,
            license.data_limit,
            license.features.join(";"),
            license_token
        ));
    }
    
    std::fs::write(&filename, csv_content)?;
    
    println!("📄 Licenses exported to: {}", filename);
    println!("📊 Total licenses: {}", licenses.len());
    
    Ok(())
}

fn print_enhanced_banner() {
    println!(r#"
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🎫 ENHANCED LICENSE GENERATOR 🎫                         ║
║                                                                              ║
║  🔒 QUANTUM-RESISTANT ENCRYPTION                                             ║
║  🥷 ADVANCED TRAFFIC OBFUSCATION                                             ║
║  🛡️  HARDWARE-BOUND AUTHENTICATION                                           ║
║  📊 REAL-TIME USAGE MONITORING                                              ║
║  🌐 GLOBAL SERVER NETWORK READY                                             ║
║  💰 ENTERPRISE-GRADE FEATURES                                               ║
║                                                                              ║
║              SECURITY LEVEL: BEYOND QUANTUM-RESISTANT                       ║
╚══════════════════════════════════════════════════════════════════════════════╝
    "#);
}

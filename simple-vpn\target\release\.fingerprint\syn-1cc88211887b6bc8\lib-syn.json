{"rustc": 16591470773350601817, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 1369601567987815722, "path": 3661791188561962712, "deps": [[1988483478007900009, "unicode_ident", false, 12514247488764560852], [3060637413840920116, "proc_macro2", false, 6844152511440372552], [17990358020177143287, "quote", false, 16571189549985695403]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\syn-1cc88211887b6bc8\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 16591470773350601817, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 1498963625094057491, "path": 854406701165259341, "deps": [[4858255257716900954, "anstyle", false, 10217609518171461714], [11166530783118767604, "strsim", false, 7093708484877864620], [12553266436076736472, "clap_lex", false, 6513413619696508592], [13237942454122161292, "anstream", false, 6267919096330601432]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\clap_builder-6b446ba4d5dd0afe\\dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
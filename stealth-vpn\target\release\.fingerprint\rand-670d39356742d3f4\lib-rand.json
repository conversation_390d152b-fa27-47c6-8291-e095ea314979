{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 17665183640080672258, "path": 10420095517970030379, "deps": [[1573238666360410412, "rand_chacha", false, 17411496316229636659], [18130209639506977569, "rand_core", false, 1422001609448145782]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rand-670d39356742d3f4\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
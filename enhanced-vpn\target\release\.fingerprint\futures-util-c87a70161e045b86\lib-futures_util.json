{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 18348216721672176038, "path": 4209264385586925791, "deps": [[5103565458935487, "futures_io", false, 4859609857951038815], [1615478164327904835, "pin_utils", false, 16071499707903421626], [1811549171721445101, "futures_channel", false, 1251880296710187498], [1906322745568073236, "pin_project_lite", false, 7433746913703061510], [3129130049864710036, "memchr", false, 12534045764483128545], [6955678925937229351, "slab", false, 810512763269877612], [7013762810557009322, "futures_sink", false, 13539126047212332334], [7620660491849607393, "futures_core", false, 10514493406049689120], [10565019901765856648, "futures_macro", false, 8278020090431451815], [16240732885093539806, "futures_task", false, 10800341202481732580]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-util-c87a70161e045b86\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
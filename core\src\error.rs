//! Error types for the UltraSecure VPN core

use thiserror::Error;

/// Main error type for VPN operations
#[derive(Error, Debug)]
pub enum VpnError {
    #[error("Cryptographic error: {0}")]
    CryptoError(String),
    
    #[error("Network error: {0}")]
    NetworkError(String),
    
    #[error("Protocol error: {0}")]
    ProtocolError(String),
    
    #[error("License error: {0}")]
    LicenseError(String),
    
    #[error("Serialization error: {0}")]
    SerializationError(String),
    
    #[error("Obfuscation error: {0}")]
    ObfuscationError(String),
    
    #[error("Configuration error: {0}")]
    ConfigError(String),
    
    #[error("Connection closed")]
    ConnectionClosed,
    
    #[error("Invalid state: {0}")]
    InvalidState(String),
    
    #[error("Hardware error: {0}")]
    HardwareError(String),
    
    #[error("Platform error: {0}")]
    PlatformError(String),
}

/// Result type alias for VPN operations
pub type Result<T> = std::result::Result<T, VpnError>;

impl From<std::io::Error> for VpnError {
    fn from(err: std::io::Error) -> Self {
        VpnError::NetworkError(err.to_string())
    }
}

impl From<bincode::Error> for VpnError {
    fn from(err: bincode::Error) -> Self {
        VpnError::SerializationError(err.to_string())
    }
}

[package]
name = "ultrasecure-vpn-server"
version = "0.1.0"
edition = "2021"
authors = ["UltraSecure VPN Team"]
description = "UltraSecure VPN server implementation"

[dependencies]
# Core VPN library
ultrasecure-vpn-core = { path = "../core" }

# Async runtime
tokio = { version = "1.0", features = ["full"] }
tokio-util = { version = "0.7", features = ["codec", "net"] }
futures = "0.3"

# Networking
socket2 = "0.5"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
bincode = "1.3"

# Database
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }

# Configuration
config = "0.14"
clap = { version = "4.0", features = ["derive"] }
toml = "0.8"

# Utilities
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# HTTP server for management
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace"] }

# TLS
rustls = "0.22"
rustls-pemfile = "2.0"

# License management
jsonwebtoken = "9.0"

[dev-dependencies]
criterion = "0.5"

[[bin]]
name = "server"
path = "src/main.rs"

[profile.release]
lto = true
codegen-units = 1
panic = "abort"
strip = true

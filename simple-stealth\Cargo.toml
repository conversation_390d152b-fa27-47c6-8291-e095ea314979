[package]
name = "business-network-tool"
version = "0.1.0"
edition = "2021"
description = "Business Network Management Tool"

[dependencies]
# Minimal, clean dependencies
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
uuid = { version = "1.0", features = ["v4"] }
base64 = "0.22"
clap = { version = "4.0", features = ["derive"] }

[[bin]]
name = "business-server"
path = "src/server.rs"

[[bin]]
name = "business-client"
path = "src/client.rs"

[[bin]]
name = "license-gen"
path = "src/license.rs"

[profile.release]
opt-level = 3
lto = true
strip = true

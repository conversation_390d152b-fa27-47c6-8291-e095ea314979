[package]
name = "ultrasecure-vpn-core"
version = "0.1.0"
edition = "2021"
authors = ["UltraSecure VPN Team"]
description = "Core VPN engine with custom protocol and triple-layer encryption"

[dependencies]
# Async runtime
tokio = { version = "1.0", features = ["full"] }
tokio-util = { version = "0.7", features = ["codec", "net"] }
futures = "0.3"

# Cryptography - Custom implementations
chacha20poly1305 = "0.10"  # XChaCha20-Poly1305
aes-gcm = "0.10"           # AES-256-GCM
blake3 = "1.5"             # Hashing
x25519-dalek = "2.0"       # Key exchange
ed25519-dalek = "2.0"      # Digital signatures
rand = "0.8"               # Secure random

# Networking
socket2 = "0.5"
trust-dns-resolver = "0.23"

# Serialization
serde = { version = "1.0", features = ["derive"] }
bincode = "1.3"
base64 = "0.22"
hex = "0.4"

# Utilities
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
thiserror = "1.0"
anyhow = "1.0"
crc32fast = "1.3"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Platform-specific networking
[target.'cfg(windows)'.dependencies]
windows = { version = "0.52", features = ["Win32_NetworkManagement_IpHelper", "Win32_Networking_WinSock"] }

[target.'cfg(unix)'.dependencies]
libc = "0.2"

[dev-dependencies]
criterion = "0.5"
tokio-test = "0.4"

[lib]
name = "ultrasecure_vpn_core"
path = "src/lib.rs"

[profile.release]
lto = true
codegen-units = 1
panic = "abort"
strip = true
opt-level = 3

[profile.dev]
opt-level = 0
debug = true

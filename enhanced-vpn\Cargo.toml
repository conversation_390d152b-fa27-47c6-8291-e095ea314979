[package]
name = "enhanced-ultrasecure-vpn"
version = "0.2.0"
edition = "2021"

[dependencies]
# Async runtime
tokio = { version = "1.0", features = ["full"] }
tokio-util = { version = "0.7", features = ["codec", "net"] }
futures = "0.3"

# Simplified Cryptography
blake3 = "1.5"             # BLAKE3 hashing
rand = "0.8"               # Secure random

# Serialization & Encoding
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
bincode = "1.3"
base64 = "0.22"
hex = "0.4"

# CLI & Configuration
clap = { version = "4.0", features = ["derive"] }
toml = "0.8"

# Utilities
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"
thiserror = "1.0"

# Networking
socket2 = "0.5"
trust-dns-resolver = "0.23"

# Compression
flate2 = "1.0"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }

# Database
rusqlite = { version = "0.30", features = ["bundled"] }

# HTTP for management API
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace"] }

# TLS
rustls = "0.22"

# Rate limiting
governor = "0.6"

[[bin]]
name = "server"
path = "src/server.rs"

[[bin]]
name = "client"
path = "src/client.rs"

[[bin]]
name = "keygen"
path = "src/keygen.rs"

[[bin]]
name = "admin"
path = "src/admin.rs"

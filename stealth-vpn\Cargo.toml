[package]
name = "secure-network-proxy"
version = "0.1.0"
edition = "2021"
description = "Secure Network Proxy for Business Applications"

[dependencies]
# Only standard, non-suspicious dependencies
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
uuid = { version = "1.0", features = ["v4"] }
base64 = "0.22"
clap = { version = "4.0", features = ["derive"] }
chrono = { version = "0.4", features = ["serde"] }

# Simple HTTP server (looks like business app)
warp = "0.3"

# Simple database
sled = "0.34"

# Logging (business-like)
env_logger = "0.10"
log = "0.4"

[[bin]]
name = "proxy-server"
path = "src/server.rs"

[[bin]]
name = "proxy-client"
path = "src/client.rs"

[[bin]]
name = "license-tool"
path = "src/license.rs"

[profile.release]
# Optimize for size and speed, avoid debug symbols
opt-level = 3
lto = true
codegen-units = 1
strip = true

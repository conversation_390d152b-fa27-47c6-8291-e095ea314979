{"rustc": 16591470773350601817, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 1498963625094057491, "path": 11833394414794104282, "deps": [[3019522439560520108, "clap_builder", false, 11849666534064805349], [17056525256108235978, "clap_derive", false, 10635515414855887990]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\clap-0333e3203d452e2f\\dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
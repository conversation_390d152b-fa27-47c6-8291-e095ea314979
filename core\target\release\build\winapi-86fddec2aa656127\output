cargo:rerun-if-changed=build.rs
cargo:rerun-if-env-changed=WINAPI_NO_BUNDLED_LIBRARIES
cargo:rerun-if-env-changed=WINAPI_STATIC_NOBUNDLE
cargo:rustc-cfg=feature="winnt"
cargo:rustc-cfg=feature="ntdef"
cargo:rustc-cfg=feature="minwindef"
cargo:rustc-cfg=feature="wincontypes"
cargo:rustc-cfg=feature="minwinbase"
cargo:rustc-cfg=feature="ktmtypes"
cargo:rustc-cfg=feature="guiddef"
cargo:rustc-cfg=feature="wingdi"
cargo:rustc-cfg=feature="ntstatus"
cargo:rustc-cfg=feature="vcruntime"
cargo:rustc-cfg=feature="windef"
cargo:rustc-cfg=feature="basetsd"
cargo:rustc-cfg=feature="wincon"
cargo:rustc-cfg=feature="excpt"
cargo:rustc-link-lib=dylib=gdi32
cargo:rustc-link-lib=dylib=kernel32
cargo:rustc-link-lib=dylib=msimg32
cargo:rustc-link-lib=dylib=opengl32
cargo:rustc-link-lib=dylib=winspool

[package]
name = "simple-ultrasecure-vpn"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
clap = { version = "4.0", features = ["derive"] }
uuid = { version = "1.0", features = ["v4"] }
base64 = "0.22"
rand = "0.8"

[[bin]]
name = "server"
path = "src/server.rs"

[[bin]]
name = "client"
path = "src/client.rs"

[[bin]]
name = "keygen"
path = "src/keygen.rs"

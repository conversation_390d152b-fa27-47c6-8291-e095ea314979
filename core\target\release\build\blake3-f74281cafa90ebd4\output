cargo:rustc-check-cfg=cfg(blake3_sse2_ffi, values(none()))
cargo:rustc-check-cfg=cfg(blake3_sse2_rust, values(none()))
cargo:rustc-check-cfg=cfg(blake3_sse41_ffi, values(none()))
cargo:rustc-check-cfg=cfg(blake3_sse41_rust, values(none()))
cargo:rustc-check-cfg=cfg(blake3_avx2_ffi, values(none()))
cargo:rustc-check-cfg=cfg(blake3_avx2_rust, values(none()))
cargo:rustc-check-cfg=cfg(blake3_avx512_ffi, values(none()))
cargo:rustc-check-cfg=cfg(blake3_neon, values(none()))
cargo:rustc-check-cfg=cfg(blake3_wasm32_simd, values(none()))
cargo:rerun-if-env-changed=CARGO_FEATURE_PURE
cargo:rerun-if-env-changed=CARGO_FEATURE_NO_NEON
OUT_DIR = Some(C:\Users\<USER>\Documents\augment-projects\vpn\core\target\release\build\blake3-f74281cafa90ebd4\out)
TARGET = Some(x86_64-pc-windows-msvc)
VCINSTALLDIR = None
VSTEL_MSBuildProjectFullPath = None
VSCMD_ARG_VCVARS_SPECTRE = None
WindowsSdkDir = None
WindowsSDKVersion = None
LIB = None
PATH = Some(C:\Users\<USER>\Documents\augment-projects\vpn\core\target\release\deps;C:\Users\<USER>\Documents\augment-projects\vpn\core\target\release;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\Python27\;C:\Python27\Scripts;C:\Python313\Scripts\;C:\Python313\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files\Notepad++;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\NuGet;C:\xampp\php;C:\Program Files\dotnet\;C:\xampp\mysql\bin;C:\ProgramData\ComposerSetup\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\xampp\htdocs\ixat-chat\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\ruffle\bin\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\Tailscale\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files (x86)\Incredibuild;C:\Program Files (x86)\Yarn\bin\;C:\Program Files\Git\cmd;C:\Program Files\Void\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Yarn\bin;C:\Program Files (x86)\Nmap;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\bin)
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
CC_x86_64-pc-windows-msvc = None
CC_x86_64_pc_windows_msvc = None
HOST_CC = None
CC = None
OUT_DIR = Some(C:\Users\<USER>\Documents\augment-projects\vpn\core\target\release\build\blake3-f74281cafa90ebd4\out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-pc-windows-msvc)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
HOST = Some(x86_64-pc-windows-msvc)
CFLAGS = None
HOST_CFLAGS = None
CFLAGS_x86_64_pc_windows_msvc = None
CFLAGS_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CARGO_FEATURE_PREFER_INTRINSICS
cargo:rerun-if-env-changed=CARGO_FEATURE_PURE
cargo:rustc-cfg=blake3_sse2_ffi
cargo:rustc-cfg=blake3_sse41_ffi
cargo:rustc-cfg=blake3_avx2_ffi
OUT_DIR = Some(C:\Users\<USER>\Documents\augment-projects\vpn\core\target\release\build\blake3-f74281cafa90ebd4\out)
OPT_LEVEL = Some(3)
TARGET = Some(x86_64-pc-windows-msvc)
VCINSTALLDIR = None
VSTEL_MSBuildProjectFullPath = None
VSCMD_ARG_VCVARS_SPECTRE = None
WindowsSdkDir = None
WindowsSDKVersion = None
LIB = None
PATH = Some(C:\Users\<USER>\Documents\augment-projects\vpn\core\target\release\deps;C:\Users\<USER>\Documents\augment-projects\vpn\core\target\release;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\Python27\;C:\Python27\Scripts;C:\Python313\Scripts\;C:\Python313\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files\Notepad++;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\NuGet;C:\xampp\php;C:\Program Files\dotnet\;C:\xampp\mysql\bin;C:\ProgramData\ComposerSetup\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\xampp\htdocs\ixat-chat\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\ruffle\bin\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\Tailscale\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files (x86)\Incredibuild;C:\Program Files (x86)\Yarn\bin\;C:\Program Files\Git\cmd;C:\Program Files\Void\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Yarn\bin;C:\Program Files (x86)\Nmap;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\bin)
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
CC_x86_64-pc-windows-msvc = None
CC_x86_64_pc_windows_msvc = None
HOST_CC = None
CC = None
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(false)
CFLAGS = None
HOST_CFLAGS = None
CFLAGS_x86_64_pc_windows_msvc = None
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
 Assembling: c/blake3_sse2_x86-64_windows_msvc.asm
 Assembling: c/blake3_sse41_x86-64_windows_msvc.asm
 Assembling: c/blake3_avx2_x86-64_windows_msvc.asm
AR_x86_64-pc-windows-msvc = None
AR_x86_64_pc_windows_msvc = None
HOST_AR = None
AR = None
ARFLAGS = None
HOST_ARFLAGS = None
ARFLAGS_x86_64_pc_windows_msvc = None
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-search=native=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\atlmfc\lib\x64
cargo:rustc-link-lib=static=blake3_sse2_sse41_avx2_assembly
cargo:rustc-link-search=native=C:\Users\<USER>\Documents\augment-projects\vpn\core\target\release\build\blake3-f74281cafa90ebd4\out
cargo:rerun-if-env-changed=CARGO_FEATURE_PURE
cargo:rerun-if-env-changed=CARGO_FEATURE_PREFER_INTRINSICS
cargo:rustc-cfg=blake3_avx512_ffi
OUT_DIR = Some(C:\Users\<USER>\Documents\augment-projects\vpn\core\target\release\build\blake3-f74281cafa90ebd4\out)
OPT_LEVEL = Some(3)
TARGET = Some(x86_64-pc-windows-msvc)
VCINSTALLDIR = None
VSTEL_MSBuildProjectFullPath = None
VSCMD_ARG_VCVARS_SPECTRE = None
WindowsSdkDir = None
WindowsSDKVersion = None
LIB = None
PATH = Some(C:\Users\<USER>\Documents\augment-projects\vpn\core\target\release\deps;C:\Users\<USER>\Documents\augment-projects\vpn\core\target\release;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\Python27\;C:\Python27\Scripts;C:\Python313\Scripts\;C:\Python313\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files\Notepad++;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\NuGet;C:\xampp\php;C:\Program Files\dotnet\;C:\xampp\mysql\bin;C:\ProgramData\ComposerSetup\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\xampp\htdocs\ixat-chat\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\ruffle\bin\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\Tailscale\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files (x86)\Incredibuild;C:\Program Files (x86)\Yarn\bin\;C:\Program Files\Git\cmd;C:\Program Files\Void\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Yarn\bin;C:\Program Files (x86)\Nmap;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\bin)
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
CC_x86_64-pc-windows-msvc = None
CC_x86_64_pc_windows_msvc = None
HOST_CC = None
CC = None
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(false)
CFLAGS = None
HOST_CFLAGS = None
CFLAGS_x86_64_pc_windows_msvc = None
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
 Assembling: c/blake3_avx512_x86-64_windows_msvc.asm
AR_x86_64-pc-windows-msvc = None
AR_x86_64_pc_windows_msvc = None
HOST_AR = None
AR = None
ARFLAGS = None
HOST_ARFLAGS = None
ARFLAGS_x86_64_pc_windows_msvc = None
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-search=native=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\atlmfc\lib\x64
cargo:rustc-link-lib=static=blake3_avx512_assembly
cargo:rustc-link-search=native=C:\Users\<USER>\Documents\augment-projects\vpn\core\target\release\build\blake3-f74281cafa90ebd4\out
cargo:rerun-if-env-changed=CARGO_FEATURE_NEON
cargo:rerun-if-env-changed=CARGO_FEATURE_NO_NEON
cargo:rerun-if-env-changed=CARGO_FEATURE_PURE
cargo:rerun-if-env-changed=CC
cargo:rerun-if-env-changed=CFLAGS
cargo:rerun-if-changed=c\.gitignore
cargo:rerun-if-changed=c\blake3-config.cmake.in
cargo:rerun-if-changed=c\blake3.c
cargo:rerun-if-changed=c\blake3.h
cargo:rerun-if-changed=c\blake3_avx2.c
cargo:rerun-if-changed=c\blake3_avx2_x86-64_unix.S
cargo:rerun-if-changed=c\blake3_avx2_x86-64_windows_gnu.S
cargo:rerun-if-changed=c\blake3_avx2_x86-64_windows_msvc.asm
cargo:rerun-if-changed=c\blake3_avx512.c
cargo:rerun-if-changed=c\blake3_avx512_x86-64_unix.S
cargo:rerun-if-changed=c\blake3_avx512_x86-64_windows_gnu.S
cargo:rerun-if-changed=c\blake3_avx512_x86-64_windows_msvc.asm
cargo:rerun-if-changed=c\blake3_dispatch.c
cargo:rerun-if-changed=c\blake3_impl.h
cargo:rerun-if-changed=c\blake3_neon.c
cargo:rerun-if-changed=c\blake3_portable.c
cargo:rerun-if-changed=c\blake3_sse2.c
cargo:rerun-if-changed=c\blake3_sse2_x86-64_unix.S
cargo:rerun-if-changed=c\blake3_sse2_x86-64_windows_gnu.S
cargo:rerun-if-changed=c\blake3_sse2_x86-64_windows_msvc.asm
cargo:rerun-if-changed=c\blake3_sse41.c
cargo:rerun-if-changed=c\blake3_sse41_x86-64_unix.S
cargo:rerun-if-changed=c\blake3_sse41_x86-64_windows_gnu.S
cargo:rerun-if-changed=c\blake3_sse41_x86-64_windows_msvc.asm
cargo:rerun-if-changed=c\blake3_tbb.cpp
cargo:rerun-if-changed=c\cmake
cargo:rerun-if-changed=c\CMakeLists.txt
cargo:rerun-if-changed=c\CMakePresets.json
cargo:rerun-if-changed=c\dependencies
cargo:rerun-if-changed=c\example.c
cargo:rerun-if-changed=c\example_tbb.c
cargo:rerun-if-changed=c\libblake3.pc.in
cargo:rerun-if-changed=c\main.c
cargo:rerun-if-changed=c\Makefile.testing
cargo:rerun-if-changed=c\README.md
cargo:rerun-if-changed=c\test.py

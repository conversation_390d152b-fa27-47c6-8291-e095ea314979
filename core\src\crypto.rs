//! Custom cryptographic implementations for UltraSecure VPN
//! 
//! This module implements our triple-layer encryption system:
//! 1. XChaCha20-Poly1305 (primary encryption)
//! 2. AES-256-GCM (secondary encryption)
//! 3. Custom stream cipher (obfuscation layer)

use crate::error::{VpnError, Result};
use chacha20poly1305::{XChaCha20Poly1305, Key, XNonce, aead::{Aead, KeyInit}};
use aes_gcm::{Aes256Gcm, Nonce};
use x25519_dalek::{EphemeralSecret, PublicKey, StaticSecret};
use ed25519_dalek::{Keypair, Signature, Signer, Verifier};
use blake3::Hasher;
use rand::{RngCore, rngs::OsRng};
use zeroize::{Zeroize, ZeroizeOnDrop};
use std::time::{SystemTime, UNIX_EPOCH};

/// Size of encryption keys in bytes
pub const KEY_SIZE: usize = 32;
/// Size of nonces in bytes
pub const NONCE_SIZE: usize = 24;
/// Size of AES nonces in bytes
pub const AES_NONCE_SIZE: usize = 12;

/// Cryptographic state for the VPN connection
#[derive(ZeroizeOnDrop)]
pub struct CryptoState {
    /// Primary encryption key (XChaCha20-Poly1305)
    primary_key: [u8; KEY_SIZE],
    /// Secondary encryption key (AES-256-GCM)
    secondary_key: [u8; KEY_SIZE],
    /// Obfuscation key (custom stream cipher)
    obfuscation_key: [u8; KEY_SIZE],
    /// Key derivation counter for forward secrecy
    key_counter: u64,
    /// Last key rotation time
    last_rotation: u64,
}

impl CryptoState {
    /// Create new cryptographic state with random keys
    pub fn new() -> Result<Self> {
        let mut primary_key = [0u8; KEY_SIZE];
        let mut secondary_key = [0u8; KEY_SIZE];
        let mut obfuscation_key = [0u8; KEY_SIZE];
        
        OsRng.fill_bytes(&mut primary_key);
        OsRng.fill_bytes(&mut secondary_key);
        OsRng.fill_bytes(&mut obfuscation_key);
        
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|e| VpnError::CryptoError(format!("Time error: {}", e)))?
            .as_secs();
        
        Ok(Self {
            primary_key,
            secondary_key,
            obfuscation_key,
            key_counter: 0,
            last_rotation: now,
        })
    }
    
    /// Update keys from shared secret (after key exchange)
    pub fn update_keys(&mut self, shared_secret: [u8; 32]) -> Result<()> {
        // Derive three keys from shared secret using BLAKE3
        let mut hasher = Hasher::new();
        hasher.update(&shared_secret);
        hasher.update(b"primary_key");
        hasher.update(&self.key_counter.to_le_bytes());
        let primary_hash = hasher.finalize();
        self.primary_key.copy_from_slice(&primary_hash.as_bytes()[..KEY_SIZE]);
        
        let mut hasher = Hasher::new();
        hasher.update(&shared_secret);
        hasher.update(b"secondary_key");
        hasher.update(&self.key_counter.to_le_bytes());
        let secondary_hash = hasher.finalize();
        self.secondary_key.copy_from_slice(&secondary_hash.as_bytes()[..KEY_SIZE]);
        
        let mut hasher = Hasher::new();
        hasher.update(&shared_secret);
        hasher.update(b"obfuscation_key");
        hasher.update(&self.key_counter.to_le_bytes());
        let obfuscation_hash = hasher.finalize();
        self.obfuscation_key.copy_from_slice(&obfuscation_hash.as_bytes()[..KEY_SIZE]);
        
        self.key_counter += 1;
        
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|e| VpnError::CryptoError(format!("Time error: {}", e)))?
            .as_secs();
        self.last_rotation = now;
        
        Ok(())
    }
    
    /// Encrypt data using triple-layer encryption
    pub fn encrypt(&self, plaintext: &[u8]) -> Result<Vec<u8>> {
        // Layer 1: XChaCha20-Poly1305
        let primary_cipher = XChaCha20Poly1305::new(Key::from_slice(&self.primary_key));
        let mut nonce = [0u8; NONCE_SIZE];
        OsRng.fill_bytes(&mut nonce);
        let xnonce = XNonce::from_slice(&nonce);
        
        let layer1 = primary_cipher.encrypt(xnonce, plaintext)
            .map_err(|e| VpnError::CryptoError(format!("Primary encryption failed: {}", e)))?;
        
        // Layer 2: AES-256-GCM
        let secondary_cipher = Aes256Gcm::new(aes_gcm::Key::<Aes256Gcm>::from_slice(&self.secondary_key));
        let mut aes_nonce = [0u8; AES_NONCE_SIZE];
        OsRng.fill_bytes(&mut aes_nonce);
        let aes_nonce_obj = Nonce::from_slice(&aes_nonce);
        
        let layer2 = secondary_cipher.encrypt(aes_nonce_obj, layer1.as_ref())
            .map_err(|e| VpnError::CryptoError(format!("Secondary encryption failed: {}", e)))?;
        
        // Layer 3: Custom stream cipher (XOR with key-derived stream)
        let layer3 = self.stream_cipher_encrypt(&layer2)?;
        
        // Prepend nonces to ciphertext
        let mut result = Vec::with_capacity(NONCE_SIZE + AES_NONCE_SIZE + layer3.len());
        result.extend_from_slice(&nonce);
        result.extend_from_slice(&aes_nonce);
        result.extend_from_slice(&layer3);
        
        Ok(result)
    }
    
    /// Decrypt data using triple-layer decryption
    pub fn decrypt(&self, ciphertext: &[u8]) -> Result<Vec<u8>> {
        if ciphertext.len() < NONCE_SIZE + AES_NONCE_SIZE {
            return Err(VpnError::CryptoError("Ciphertext too short".to_string()));
        }
        
        // Extract nonces
        let nonce = &ciphertext[..NONCE_SIZE];
        let aes_nonce = &ciphertext[NONCE_SIZE..NONCE_SIZE + AES_NONCE_SIZE];
        let encrypted_data = &ciphertext[NONCE_SIZE + AES_NONCE_SIZE..];
        
        // Layer 3: Custom stream cipher (reverse)
        let layer2_data = self.stream_cipher_decrypt(encrypted_data)?;
        
        // Layer 2: AES-256-GCM (reverse)
        let secondary_cipher = Aes256Gcm::new(aes_gcm::Key::<Aes256Gcm>::from_slice(&self.secondary_key));
        let aes_nonce_obj = Nonce::from_slice(aes_nonce);
        
        let layer1_data = secondary_cipher.decrypt(aes_nonce_obj, layer2_data.as_ref())
            .map_err(|e| VpnError::CryptoError(format!("Secondary decryption failed: {}", e)))?;
        
        // Layer 1: XChaCha20-Poly1305 (reverse)
        let primary_cipher = XChaCha20Poly1305::new(Key::from_slice(&self.primary_key));
        let xnonce = XNonce::from_slice(nonce);
        
        let plaintext = primary_cipher.decrypt(xnonce, layer1_data.as_ref())
            .map_err(|e| VpnError::CryptoError(format!("Primary decryption failed: {}", e)))?;
        
        Ok(plaintext)
    }
    
    /// Custom stream cipher for obfuscation layer
    fn stream_cipher_encrypt(&self, data: &[u8]) -> Result<Vec<u8>> {
        let keystream = self.generate_keystream(data.len())?;
        let mut result = Vec::with_capacity(data.len());
        
        for (byte, key_byte) in data.iter().zip(keystream.iter()) {
            result.push(byte ^ key_byte);
        }
        
        Ok(result)
    }
    
    /// Custom stream cipher for obfuscation layer (decrypt)
    fn stream_cipher_decrypt(&self, data: &[u8]) -> Result<Vec<u8>> {
        // XOR is symmetric, so decryption is the same as encryption
        self.stream_cipher_encrypt(data)
    }
    
    /// Generate keystream for custom cipher
    fn generate_keystream(&self, length: usize) -> Result<Vec<u8>> {
        let mut hasher = Hasher::new();
        hasher.update(&self.obfuscation_key);
        hasher.update(&self.key_counter.to_le_bytes());
        
        let mut keystream = Vec::with_capacity(length);
        let mut counter = 0u64;
        
        while keystream.len() < length {
            let mut block_hasher = hasher.clone();
            block_hasher.update(&counter.to_le_bytes());
            let hash = block_hasher.finalize();
            
            let bytes_needed = std::cmp::min(32, length - keystream.len());
            keystream.extend_from_slice(&hash.as_bytes()[..bytes_needed]);
            counter += 1;
        }
        
        Ok(keystream)
    }
    
    /// Check if keys need rotation (every 1 hour)
    pub fn needs_key_rotation(&self) -> Result<bool> {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|e| VpnError::CryptoError(format!("Time error: {}", e)))?
            .as_secs();
        
        Ok(now - self.last_rotation > 3600) // 1 hour
    }
}

/// Generate X25519 keypair for key exchange
pub fn generate_keypair() -> Result<(PublicKey, EphemeralSecret)> {
    let private_key = EphemeralSecret::new(OsRng);
    let public_key = PublicKey::from(&private_key);
    Ok((public_key, private_key))
}

/// Derive shared secret from private key and peer's public key
pub fn derive_shared_secret(private_key: &EphemeralSecret, peer_public: &PublicKey) -> Result<[u8; 32]> {
    let shared_secret = private_key.diffie_hellman(peer_public);
    Ok(*shared_secret.as_bytes())
}

/// Generate Ed25519 signing keypair
pub fn generate_signing_keypair() -> Result<Keypair> {
    let mut csprng = OsRng;
    Ok(Keypair::generate(&mut csprng))
}

/// Sign data with Ed25519
pub fn sign_data(keypair: &Keypair, data: &[u8]) -> Result<Signature> {
    Ok(keypair.sign(data))
}

/// Verify Ed25519 signature
pub fn verify_signature(public_key: &ed25519_dalek::PublicKey, data: &[u8], signature: &Signature) -> Result<()> {
    public_key.verify(data, signature)
        .map_err(|e| VpnError::CryptoError(format!("Signature verification failed: {}", e)))
}

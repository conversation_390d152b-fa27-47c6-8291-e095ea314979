//! TUN/TAP interface management for cross-platform VPN tunneling
//! 
//! This module provides cross-platform TUN interface creation and management
//! for routing traffic through the VPN tunnel.

use crate::error::{VpnError, Result};
use std::net::IpAddr;
use tokio::io::{AsyncReadExt, AsyncWriteExt};

/// TUN interface for packet routing
pub struct TunInterface {
    /// Interface name
    name: String,
    /// Assigned IP address
    ip_address: IpAddr,
    /// Network mask
    netmask: IpAddr,
    /// Platform-specific handle
    #[cfg(unix)]
    fd: std::os::unix::io::RawFd,
    #[cfg(windows)]
    handle: windows::Win32::Foundation::HANDLE,
}

impl TunInterface {
    /// Create a new TUN interface
    pub async fn new(name: String, ip_address: IpAddr, netmask: IpAddr) -> Result<Self> {
        #[cfg(target_os = "linux")]
        {
            Self::create_linux_tun(name, ip_address, netmask).await
        }
        
        #[cfg(target_os = "macos")]
        {
            Self::create_macos_tun(name, ip_address, netmask).await
        }
        
        #[cfg(target_os = "windows")]
        {
            Self::create_windows_tun(name, ip_address, netmask).await
        }
        
        #[cfg(not(any(target_os = "linux", target_os = "macos", target_os = "windows")))]
        {
            Err(VpnError::PlatformError("Unsupported platform for TUN interface".to_string()))
        }
    }
    
    /// Read packet from TUN interface
    pub async fn read_packet(&mut self) -> Result<Vec<u8>> {
        #[cfg(unix)]
        {
            let mut buffer = vec![0u8; 65536];
            let bytes_read = unsafe {
                libc::read(self.fd, buffer.as_mut_ptr() as *mut libc::c_void, buffer.len())
            };
            
            if bytes_read < 0 {
                return Err(VpnError::NetworkError("Failed to read from TUN interface".to_string()));
            }
            
            buffer.truncate(bytes_read as usize);
            Ok(buffer)
        }
        
        #[cfg(windows)]
        {
            // Windows TUN implementation would go here
            // This is more complex and requires WinTUN or TAP-Windows adapter
            Err(VpnError::PlatformError("Windows TUN not implemented yet".to_string()))
        }
    }
    
    /// Write packet to TUN interface
    pub async fn write_packet(&mut self, packet: &[u8]) -> Result<()> {
        #[cfg(unix)]
        {
            let bytes_written = unsafe {
                libc::write(self.fd, packet.as_ptr() as *const libc::c_void, packet.len())
            };
            
            if bytes_written < 0 || bytes_written as usize != packet.len() {
                return Err(VpnError::NetworkError("Failed to write to TUN interface".to_string()));
            }
            
            Ok(())
        }
        
        #[cfg(windows)]
        {
            // Windows TUN implementation would go here
            Err(VpnError::PlatformError("Windows TUN not implemented yet".to_string()))
        }
    }
    
    /// Get interface name
    pub fn get_name(&self) -> &str {
        &self.name
    }
    
    /// Get assigned IP address
    pub fn get_ip_address(&self) -> IpAddr {
        self.ip_address
    }
    
    #[cfg(target_os = "linux")]
    async fn create_linux_tun(name: String, ip_address: IpAddr, netmask: IpAddr) -> Result<Self> {
        use std::os::unix::io::AsRawFd;
        use std::ffi::CString;
        
        // Open TUN device
        let tun_fd = unsafe {
            libc::open(
                CString::new("/dev/net/tun").unwrap().as_ptr(),
                libc::O_RDWR | libc::O_NONBLOCK,
            )
        };
        
        if tun_fd < 0 {
            return Err(VpnError::PlatformError("Failed to open /dev/net/tun".to_string()));
        }
        
        // Configure TUN interface
        let mut ifr: libc::ifreq = unsafe { std::mem::zeroed() };
        let name_bytes = name.as_bytes();
        let copy_len = std::cmp::min(name_bytes.len(), libc::IFNAMSIZ - 1);
        
        unsafe {
            std::ptr::copy_nonoverlapping(
                name_bytes.as_ptr(),
                ifr.ifr_name.as_mut_ptr() as *mut u8,
                copy_len,
            );
        }
        
        ifr.ifr_ifru.ifru_flags = (libc::IFF_TUN | libc::IFF_NO_PI) as i16;
        
        let result = unsafe {
            libc::ioctl(tun_fd, libc::TUNSETIFF as libc::c_ulong, &ifr)
        };
        
        if result < 0 {
            unsafe { libc::close(tun_fd); }
            return Err(VpnError::PlatformError("Failed to configure TUN interface".to_string()));
        }
        
        // Set IP address using system commands
        let ip_str = ip_address.to_string();
        let netmask_str = netmask.to_string();
        
        let output = std::process::Command::new("ip")
            .args(&["addr", "add", &format!("{}/{}", ip_str, "24"), "dev", &name])
            .output()
            .map_err(|e| VpnError::PlatformError(format!("Failed to set IP address: {}", e)))?;
        
        if !output.status.success() {
            return Err(VpnError::PlatformError("Failed to configure IP address".to_string()));
        }
        
        // Bring interface up
        let output = std::process::Command::new("ip")
            .args(&["link", "set", "dev", &name, "up"])
            .output()
            .map_err(|e| VpnError::PlatformError(format!("Failed to bring interface up: {}", e)))?;
        
        if !output.status.success() {
            return Err(VpnError::PlatformError("Failed to bring interface up".to_string()));
        }
        
        Ok(Self {
            name,
            ip_address,
            netmask,
            fd: tun_fd,
        })
    }
    
    #[cfg(target_os = "macos")]
    async fn create_macos_tun(name: String, ip_address: IpAddr, netmask: IpAddr) -> Result<Self> {
        // macOS TUN implementation
        // This would use utun interfaces on macOS
        Err(VpnError::PlatformError("macOS TUN not implemented yet".to_string()))
    }
    
    #[cfg(target_os = "windows")]
    async fn create_windows_tun(name: String, ip_address: IpAddr, netmask: IpAddr) -> Result<Self> {
        // Windows TUN implementation using WinTUN
        // This requires the WinTUN library
        Err(VpnError::PlatformError("Windows TUN not implemented yet".to_string()))
    }
}

impl Drop for TunInterface {
    fn drop(&mut self) {
        #[cfg(unix)]
        {
            unsafe {
                libc::close(self.fd);
            }
        }
        
        #[cfg(windows)]
        {
            // Close Windows handle
        }
    }
}

/// Route table management
pub struct RouteManager {
    /// Original default route (for restoration)
    original_default_route: Option<String>,
}

impl RouteManager {
    /// Create new route manager
    pub fn new() -> Self {
        Self {
            original_default_route: None,
        }
    }
    
    /// Add route through VPN
    pub async fn add_vpn_route(&mut self, destination: &str, gateway: &str, interface: &str) -> Result<()> {
        #[cfg(target_os = "linux")]
        {
            let output = std::process::Command::new("ip")
                .args(&["route", "add", destination, "via", gateway, "dev", interface])
                .output()
                .map_err(|e| VpnError::PlatformError(format!("Failed to add route: {}", e)))?;
            
            if !output.status.success() {
                return Err(VpnError::PlatformError("Failed to add VPN route".to_string()));
            }
        }
        
        #[cfg(target_os = "macos")]
        {
            let output = std::process::Command::new("route")
                .args(&["add", destination, gateway])
                .output()
                .map_err(|e| VpnError::PlatformError(format!("Failed to add route: {}", e)))?;
            
            if !output.status.success() {
                return Err(VpnError::PlatformError("Failed to add VPN route".to_string()));
            }
        }
        
        #[cfg(target_os = "windows")]
        {
            let output = std::process::Command::new("route")
                .args(&["add", destination, gateway])
                .output()
                .map_err(|e| VpnError::PlatformError(format!("Failed to add route: {}", e)))?;
            
            if !output.status.success() {
                return Err(VpnError::PlatformError("Failed to add VPN route".to_string()));
            }
        }
        
        Ok(())
    }
    
    /// Remove route
    pub async fn remove_route(&self, destination: &str) -> Result<()> {
        #[cfg(target_os = "linux")]
        {
            let output = std::process::Command::new("ip")
                .args(&["route", "del", destination])
                .output()
                .map_err(|e| VpnError::PlatformError(format!("Failed to remove route: {}", e)))?;
            
            if !output.status.success() {
                return Err(VpnError::PlatformError("Failed to remove route".to_string()));
            }
        }
        
        #[cfg(target_os = "macos")]
        {
            let output = std::process::Command::new("route")
                .args(&["delete", destination])
                .output()
                .map_err(|e| VpnError::PlatformError(format!("Failed to remove route: {}", e)))?;
            
            if !output.status.success() {
                return Err(VpnError::PlatformError("Failed to remove route".to_string()));
            }
        }
        
        #[cfg(target_os = "windows")]
        {
            let output = std::process::Command::new("route")
                .args(&["delete", destination])
                .output()
                .map_err(|e| VpnError::PlatformError(format!("Failed to remove route: {}", e)))?;
            
            if !output.status.success() {
                return Err(VpnError::PlatformError("Failed to remove route".to_string()));
            }
        }
        
        Ok(())
    }
    
    /// Redirect all traffic through VPN
    pub async fn redirect_all_traffic(&mut self, vpn_gateway: &str, vpn_interface: &str) -> Result<()> {
        // Store original default route for restoration
        self.store_original_route().await?;
        
        // Add specific routes for VPN server to avoid routing loops
        // This is a simplified implementation
        
        // Redirect all traffic through VPN
        self.add_vpn_route("0.0.0.0/0", vpn_gateway, vpn_interface).await?;
        
        Ok(())
    }
    
    /// Restore original routing
    pub async fn restore_original_routing(&self) -> Result<()> {
        // Remove VPN routes and restore original default route
        if let Some(original_route) = &self.original_default_route {
            // Restore original route (implementation depends on stored format)
        }
        
        Ok(())
    }
    
    async fn store_original_route(&mut self) -> Result<()> {
        // Store the current default route for later restoration
        // Implementation would capture current routing table state
        Ok(())
    }
}

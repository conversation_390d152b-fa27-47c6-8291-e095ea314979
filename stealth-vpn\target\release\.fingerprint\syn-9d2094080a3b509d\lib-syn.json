{"rustc": 16591470773350601817, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 17984201634715228204, "path": 3661791188561962712, "deps": [[1988483478007900009, "unicode_ident", false, 12682233261293693055], [3060637413840920116, "proc_macro2", false, 11827752392441722999], [17990358020177143287, "quote", false, 15633635655334468716]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\syn-9d2094080a3b509d\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
//! License Key Generator for UltraSecure VPN

use clap::Parser;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use std::time::{SystemTime, UNIX_EPOCH};

#[derive(Parser)]
struct Args {
    #[arg(short, long, default_value = "demo-user")]
    user_id: String,
    
    #[arg(short, long, default_value = "premium")]
    tier: String,
    
    #[arg(short, long, default_value = "30")]
    days: u64,
}

#[derive(Serialize, Deserialize)]
struct LicenseData {
    license_id: String,
    user_id: String,
    tier: String,
    expires_at: u64,
    issued_at: u64,
    max_connections: u32,
    bandwidth_limit: u64,
    features: Vec<String>,
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let args = Args::parse();
    
    println!("🎫 UltraSecure VPN License Generator");
    println!("🔒 Security Level: BEYOND MILITARY GRADE");
    println!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)?
        .as_secs();
    
    let expires_at = now + (args.days * 24 * 60 * 60);
    
    let (max_connections, bandwidth_limit, features) = match args.tier.as_str() {
        "basic" => (1, 10_000_000, vec!["encryption".to_string()]),
        "premium" => (3, 100_000_000, vec!["encryption".to_string(), "obfuscation".to_string()]),
        "enterprise" => (10, 0, vec!["encryption".to_string(), "obfuscation".to_string(), "priority".to_string()]),
        _ => (1, 10_000_000, vec!["encryption".to_string()]),
    };
    
    let license_data = LicenseData {
        license_id: Uuid::new_v4().to_string(),
        user_id: args.user_id.clone(),
        tier: args.tier.clone(),
        expires_at,
        issued_at: now,
        max_connections,
        bandwidth_limit,
        features: features.clone(),
    };
    
    // Create a simple license token
    let license_json = serde_json::to_string(&license_data)?;
    let license_token = base64::encode(&license_json);
    
    println!("✅ License Generated Successfully!");
    println!();
    println!("📋 License Details:");
    println!("   👤 User ID: {}", args.user_id);
    println!("   🏷️  Tier: {}", args.tier);
    println!("   ⏰ Valid for: {} days", args.days);
    println!("   🔗 Max Connections: {}", max_connections);
    println!("   📊 Bandwidth: {} MB/s", bandwidth_limit / 1_000_000);
    println!("   🎯 Features: {}", features.join(", "));
    println!();
    println!("🔑 License Token:");
    println!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    println!("{}", license_token);
    println!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    println!();
    println!("💡 Usage:");
    println!("   Server: cargo run --bin server");
    println!("   Client: cargo run --bin client -- --server 127.0.0.1:51820 --license \"{}\"", license_token);
    
    Ok(())
}

//! Network utilities and helpers for VPN operations

use crate::error::{VpnError, Result};
use std::net::{IpAddr, Ipv4Addr, SocketAddr};
use trust_dns_resolver::{Resolver, config::*};

/// DNS resolver with custom configuration
pub struct DnsResolver {
    resolver: Resolver,
}

impl DnsResolver {
    /// Create new DNS resolver with secure configuration
    pub fn new() -> Result<Self> {
        // Use secure DNS servers (Cloudflare, Quad9)
        let mut config = ResolverConfig::new();
        config.add_name_server(NameServerConfig {
            socket_addr: SocketAddr::new(IpAddr::V4(Ipv4Addr::new(1, 1, 1, 1)), 853),
            protocol: Protocol::Tls,
            tls_dns_name: Some("cloudflare-dns.com".to_string()),
            trust_nx_responses: false,
            bind_addr: None,
        });
        
        config.add_name_server(NameServerConfig {
            socket_addr: SocketAddr::new(IpAddr::V4(Ipv4Addr::new(9, 9, 9, 9)), 853),
            protocol: Protocol::Tls,
            tls_dns_name: Some("dns.quad9.net".to_string()),
            trust_nx_responses: false,
            bind_addr: None,
        });
        
        let opts = ResolverOpts::default();
        let resolver = Resolver::new(config, opts)
            .map_err(|e| VpnError::NetworkError(format!("Failed to create DNS resolver: {}", e)))?;
        
        Ok(Self { resolver })
    }
    
    /// Resolve hostname to IP address
    pub async fn resolve(&self, hostname: &str) -> Result<IpAddr> {
        let response = self.resolver.lookup_ip(hostname)
            .map_err(|e| VpnError::NetworkError(format!("DNS resolution failed: {}", e)))?;
        
        response.iter().next()
            .ok_or_else(|| VpnError::NetworkError("No IP addresses found".to_string()))
    }
}

/// Network interface utilities
pub struct NetworkInterface;

impl NetworkInterface {
    /// Get all network interfaces
    pub fn get_interfaces() -> Result<Vec<InterfaceInfo>> {
        let mut interfaces = Vec::new();
        
        #[cfg(unix)]
        {
            // Use getifaddrs on Unix systems
            use std::ffi::CStr;
            use std::ptr;
            
            unsafe {
                let mut ifaddrs: *mut libc::ifaddrs = ptr::null_mut();
                if libc::getifaddrs(&mut ifaddrs) != 0 {
                    return Err(VpnError::NetworkError("Failed to get network interfaces".to_string()));
                }
                
                let mut current = ifaddrs;
                while !current.is_null() {
                    let ifa = &*current;
                    
                    if !ifa.ifa_addr.is_null() {
                        let name = CStr::from_ptr(ifa.ifa_name).to_string_lossy().to_string();
                        
                        // Extract IP address based on family
                        let addr_family = (*ifa.ifa_addr).sa_family;
                        if addr_family == libc::AF_INET as u16 {
                            let sockaddr_in = ifa.ifa_addr as *const libc::sockaddr_in;
                            let ip_addr = Ipv4Addr::from((*sockaddr_in).sin_addr.s_addr.to_be());
                            
                            interfaces.push(InterfaceInfo {
                                name,
                                ip_address: IpAddr::V4(ip_addr),
                                is_up: (ifa.ifa_flags & libc::IFF_UP as u32) != 0,
                                is_loopback: (ifa.ifa_flags & libc::IFF_LOOPBACK as u32) != 0,
                            });
                        }
                    }
                    
                    current = ifa.ifa_next;
                }
                
                libc::freeifaddrs(ifaddrs);
            }
        }
        
        #[cfg(windows)]
        {
            // Windows implementation would use GetAdaptersAddresses
            return Err(VpnError::PlatformError("Windows interface enumeration not implemented".to_string()));
        }
        
        Ok(interfaces)
    }
    
    /// Get default gateway
    pub fn get_default_gateway() -> Result<IpAddr> {
        #[cfg(target_os = "linux")]
        {
            let output = std::process::Command::new("ip")
                .args(&["route", "show", "default"])
                .output()
                .map_err(|e| VpnError::NetworkError(format!("Failed to get default gateway: {}", e)))?;
            
            let output_str = String::from_utf8_lossy(&output.stdout);
            for line in output_str.lines() {
                if line.contains("default via") {
                    let parts: Vec<&str> = line.split_whitespace().collect();
                    if parts.len() >= 3 {
                        return parts[2].parse()
                            .map_err(|e| VpnError::NetworkError(format!("Invalid gateway IP: {}", e)));
                    }
                }
            }
        }
        
        #[cfg(target_os = "macos")]
        {
            let output = std::process::Command::new("route")
                .args(&["get", "default"])
                .output()
                .map_err(|e| VpnError::NetworkError(format!("Failed to get default gateway: {}", e)))?;
            
            let output_str = String::from_utf8_lossy(&output.stdout);
            for line in output_str.lines() {
                if line.trim().starts_with("gateway:") {
                    let gateway = line.split(':').nth(1)
                        .ok_or_else(|| VpnError::NetworkError("Invalid gateway format".to_string()))?
                        .trim();
                    return gateway.parse()
                        .map_err(|e| VpnError::NetworkError(format!("Invalid gateway IP: {}", e)));
                }
            }
        }
        
        #[cfg(target_os = "windows")]
        {
            let output = std::process::Command::new("route")
                .args(&["print", "0.0.0.0"])
                .output()
                .map_err(|e| VpnError::NetworkError(format!("Failed to get default gateway: {}", e)))?;
            
            let output_str = String::from_utf8_lossy(&output.stdout);
            // Parse Windows route output
            // This is a simplified implementation
            return Err(VpnError::PlatformError("Windows gateway detection not fully implemented".to_string()));
        }
        
        Err(VpnError::NetworkError("Could not determine default gateway".to_string()))
    }
    
    /// Check if an IP address is reachable
    pub async fn is_reachable(ip: IpAddr, port: u16, timeout_ms: u64) -> bool {
        use tokio::time::{timeout, Duration};
        use tokio::net::TcpStream;
        
        let addr = SocketAddr::new(ip, port);
        let connect_future = TcpStream::connect(addr);
        
        match timeout(Duration::from_millis(timeout_ms), connect_future).await {
            Ok(Ok(_)) => true,
            _ => false,
        }
    }
}

/// Information about a network interface
#[derive(Debug, Clone)]
pub struct InterfaceInfo {
    pub name: String,
    pub ip_address: IpAddr,
    pub is_up: bool,
    pub is_loopback: bool,
}

/// Network bandwidth monitor
pub struct BandwidthMonitor {
    bytes_sent: u64,
    bytes_received: u64,
    start_time: std::time::Instant,
}

impl BandwidthMonitor {
    /// Create new bandwidth monitor
    pub fn new() -> Self {
        Self {
            bytes_sent: 0,
            bytes_received: 0,
            start_time: std::time::Instant::now(),
        }
    }
    
    /// Record sent bytes
    pub fn record_sent(&mut self, bytes: u64) {
        self.bytes_sent += bytes;
    }
    
    /// Record received bytes
    pub fn record_received(&mut self, bytes: u64) {
        self.bytes_received += bytes;
    }
    
    /// Get current upload speed in bytes per second
    pub fn get_upload_speed(&self) -> f64 {
        let elapsed = self.start_time.elapsed().as_secs_f64();
        if elapsed > 0.0 {
            self.bytes_sent as f64 / elapsed
        } else {
            0.0
        }
    }
    
    /// Get current download speed in bytes per second
    pub fn get_download_speed(&self) -> f64 {
        let elapsed = self.start_time.elapsed().as_secs_f64();
        if elapsed > 0.0 {
            self.bytes_received as f64 / elapsed
        } else {
            0.0
        }
    }
    
    /// Get total bytes transferred
    pub fn get_total_bytes(&self) -> (u64, u64) {
        (self.bytes_sent, self.bytes_received)
    }
    
    /// Reset statistics
    pub fn reset(&mut self) {
        self.bytes_sent = 0;
        self.bytes_received = 0;
        self.start_time = std::time::Instant::now();
    }
}

/// Network latency measurement
pub struct LatencyMeasurement;

impl LatencyMeasurement {
    /// Measure round-trip time to a server
    pub async fn measure_rtt(server_addr: SocketAddr) -> Result<Duration> {
        use tokio::time::{Instant, Duration};
        use tokio::net::UdpSocket;
        
        let socket = UdpSocket::bind("0.0.0.0:0").await
            .map_err(|e| VpnError::NetworkError(format!("Failed to bind socket: {}", e)))?;
        
        let start = Instant::now();
        
        // Send a small packet
        let test_data = b"ping";
        socket.send_to(test_data, server_addr).await
            .map_err(|e| VpnError::NetworkError(format!("Failed to send ping: {}", e)))?;
        
        // Wait for response (with timeout)
        let mut buffer = [0u8; 1024];
        let timeout_duration = Duration::from_secs(5);
        
        match tokio::time::timeout(timeout_duration, socket.recv_from(&mut buffer)).await {
            Ok(Ok(_)) => {
                let rtt = start.elapsed();
                Ok(rtt)
            }
            Ok(Err(e)) => Err(VpnError::NetworkError(format!("Failed to receive pong: {}", e))),
            Err(_) => Err(VpnError::NetworkError("Ping timeout".to_string())),
        }
    }
}

use tokio::time::Duration;

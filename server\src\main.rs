//! UltraSecure VPN Server
//! 
//! A high-performance VPN server with advanced security features

use ultrasecure_vpn_core::{VpnError, Result};
use clap::Parser;
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::net::UdpSocket;
use tracing::{info, error, debug, warn};

mod server;
mod license_manager;
mod client_manager;
mod config;

use server::VpnServer;
use config::ServerConfig;

/// Command line arguments
#[derive(Parser, Debug)]
#[command(name = "ultrasecure-vpn-server")]
#[command(about = "UltraSecure VPN Server with advanced encryption")]
struct Args {
    /// Configuration file path
    #[arg(short, long, default_value = "server.toml")]
    config: String,
    
    /// Server bind address
    #[arg(short, long, default_value = "0.0.0.0:51820")]
    bind: SocketAddr,
    
    /// Enable debug logging
    #[arg(short, long)]
    debug: bool,
    
    /// Database URL
    #[arg(short = 'D', long, default_value = "sqlite:vpn_server.db")]
    database_url: String,
}

#[tokio::main]
async fn main() -> Result<()> {
    let args = Args::parse();
    
    // Initialize logging
    let log_level = if args.debug { "debug" } else { "info" };
    tracing_subscriber::fmt()
        .with_env_filter(format!("ultrasecure_vpn_server={},ultrasecure_vpn_core={}", log_level, log_level))
        .init();
    
    info!("🚀 Starting UltraSecure VPN Server");
    info!("📍 Bind address: {}", args.bind);
    info!("🗄️  Database: {}", args.database_url);
    
    // Load configuration
    let config = ServerConfig::load(&args.config).await?;
    info!("⚙️  Configuration loaded from {}", args.config);
    
    // Initialize database
    let db_pool = sqlx::SqlitePool::connect(&args.database_url).await
        .map_err(|e| VpnError::ConfigError(format!("Database connection failed: {}", e)))?;
    
    // Run database migrations
    sqlx::migrate!("./migrations").run(&db_pool).await
        .map_err(|e| VpnError::ConfigError(format!("Database migration failed: {}", e)))?;
    
    info!("💾 Database initialized");
    
    // Create and start VPN server
    let mut server = VpnServer::new(args.bind, config, db_pool).await?;
    
    // Handle shutdown gracefully
    let shutdown_signal = async {
        tokio::signal::ctrl_c()
            .await
            .expect("Failed to install CTRL+C signal handler");
        info!("🛑 Shutdown signal received");
    };
    
    // Run server with graceful shutdown
    tokio::select! {
        result = server.run() => {
            match result {
                Ok(_) => info!("✅ Server stopped normally"),
                Err(e) => error!("❌ Server error: {}", e),
            }
        }
        _ = shutdown_signal => {
            info!("🔄 Initiating graceful shutdown...");
            server.shutdown().await?;
            info!("✅ Server shutdown complete");
        }
    }
    
    Ok(())
}

/// Print banner with server information
fn print_banner() {
    println!(r#"
    ╔══════════════════════════════════════════════════════════════╗
    ║                    UltraSecure VPN Server                    ║
    ║                                                              ║
    ║  🔒 Triple-layer encryption (XChaCha20 + AES-256 + Custom)  ║
    ║  🥷 Advanced traffic obfuscation                             ║
    ║  🔑 Hardware-bound license authentication                    ║
    ║  🌐 Cross-platform support                                  ║
    ║  💰 Budget-friendly deployment                               ║
    ║                                                              ║
    ║  Security Level: BEYOND MILITARY GRADE                      ║
    ╚══════════════════════════════════════════════════════════════╝
    "#);
}

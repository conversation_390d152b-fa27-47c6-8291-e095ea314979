{"rustc": 16591470773350601817, "features": "[\"tracing\"]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 2040997289075261528, "path": 10567579170375208730, "deps": [[784494742817713399, "tower_service", false, 1861833967012313954], [1906322745568073236, "pin_project_lite", false, 7433746913703061510], [2517136641825875337, "sync_wrapper", false, 5855019149722793649], [7712452662827335977, "tower_layer", false, 1457730846194011626], [7858942147296547339, "rustversion", false, 2514165811709440288], [8606274917505247608, "tracing", false, 6723496715601820690], [9010263965687315507, "http", false, 15677836872359614113], [10229185211513642314, "mime", false, 7974677327052559545], [10629569228670356391, "futures_util", false, 11209357034906847022], [11946729385090170470, "async_trait", false, 1054679854502272457], [14084095096285906100, "http_body", false, 11748545772775035563], [16066129441945555748, "bytes", false, 6340292206522364871], [16900715236047033623, "http_body_util", false, 13122697805833452045]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\axum-core-d8c26e148c786de9\\dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
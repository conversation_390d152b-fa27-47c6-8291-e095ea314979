{"rustc": 16591470773350601817, "features": "[\"attributes\", \"default\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 3541797763817303166, "path": 14692848018672501944, "deps": [[1906322745568073236, "pin_project_lite", false, 11280453031113303869], [2967683870285097694, "tracing_attributes", false, 14552917937692421890], [11033263105862272874, "tracing_core", false, 16762748058436349872]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tracing-326ff0606eb71f30\\dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 18348216721672176038, "path": 9181209079796926805, "deps": [[5103565458935487, "futures_io", false, 4859609857951038815], [1811549171721445101, "futures_channel", false, 1251880296710187498], [7013762810557009322, "futures_sink", false, 13539126047212332334], [7620660491849607393, "futures_core", false, 10514493406049689120], [10629569228670356391, "futures_util", false, 11209357034906847022], [12779779637805422465, "futures_executor", false, 12535012876154367474], [16240732885093539806, "futures_task", false, 10800341202481732580]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-f6d89d02d47627ff\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
[package]
name = "ultrasecure-vpn-client"
version = "0.1.0"
edition = "2021"
authors = ["UltraSecure VPN Team"]
description = "UltraSecure VPN client application"

[dependencies]
# Core VPN library
ultrasecure-vpn-core = { path = "../core" }

# Async runtime
tokio = { version = "1.0", features = ["full"] }
tokio-util = { version = "0.7", features = ["codec", "net"] }
futures = "0.3"

# GUI framework (cross-platform)
eframe = "0.24"
egui = "0.24"

# Networking
socket2 = "0.5"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
bincode = "1.3"

# Configuration
clap = { version = "4.0", features = ["derive"] }
toml = "0.8"

# Utilities
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# System integration
dirs = "5.0"

# Platform-specific dependencies
[target.'cfg(windows)'.dependencies]
windows = { version = "0.52", features = [
    "Win32_NetworkManagement_IpHelper",
    "Win32_Networking_WinSock",
    "Win32_System_Registry",
    "Win32_Foundation"
]}

[target.'cfg(unix)'.dependencies]
libc = "0.2"

[target.'cfg(target_os = "macos")'.dependencies]
core-foundation = "0.9"
system-configuration = "0.5"

[[bin]]
name = "client"
path = "src/main.rs"

[[bin]]
name = "client-cli"
path = "src/cli.rs"

[profile.release]
lto = true
codegen-units = 1
panic = "abort"
strip = true

[features]
default = ["gui"]
gui = ["eframe", "egui"]
cli-only = []

@echo off
echo 🧪 Testing Rust Installation...
echo.

REM Try to add Rust to PATH
set PATH=%PATH%;%USERPROFILE%\.cargo\bin

echo Checking Rust installation...
rustc --version
if %errorlevel% neq 0 (
    echo.
    echo ❌ Rust is not properly installed or not in PATH
    echo.
    echo 🔧 Please try these steps:
    echo 1. Close this terminal completely
    echo 2. Open a new PowerShell or Command Prompt
    echo 3. Run: rustc --version
    echo 4. If that works, run: build.bat
    echo.
    echo 💡 If Rust still doesn't work:
    echo 1. Restart your computer
    echo 2. Or reinstall Rust from https://rustup.rs/
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ Rust is working! Version found.
echo.

echo Checking Cargo...
cargo --version
if %errorlevel% neq 0 (
    echo ❌ Cargo not found
    pause
    exit /b 1
)

echo.
echo ✅ Cargo is working! Version found.
echo.

echo 🎉 Rust installation is complete and working!
echo.
echo 🚀 You can now run: build.bat
echo.
pause

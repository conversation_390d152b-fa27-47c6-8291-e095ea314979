{"rustc": 16591470773350601817, "features": "[\"default\", \"form\", \"http1\", \"json\", \"matched-path\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\"]", "declared_features": "[\"__private_docs\", \"default\", \"form\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 13920321295547257648, "profile": 2040997289075261528, "path": 5638798383902198022, "deps": [[40386456601120721, "percent_encoding", false, 13947468257329953321], [784494742817713399, "tower_service", false, 1861833967012313954], [970965535607393401, "hyper_util", false, 121208056350624900], [1906322745568073236, "pin_project_lite", false, 7433746913703061510], [2517136641825875337, "sync_wrapper", false, 5855019149722793649], [3129130049864710036, "memchr", false, 12534045764483128545], [4359148418957042248, "axum_core", false, 12259656346161549088], [5695049318159433696, "tower", false, 10773711702645161718], [7695812897323945497, "itoa", false, 5307754552999915887], [7712452662827335977, "tower_layer", false, 1457730846194011626], [7858942147296547339, "rustversion", false, 2514165811709440288], [8606274917505247608, "tracing", false, 6723496715601820690], [9010263965687315507, "http", false, 15677836872359614113], [9538054652646069845, "tokio", false, 16157593641477756769], [9678799920983747518, "matchit", false, 3290059823837419985], [9689903380558560274, "serde", false, 13453297982564006924], [10229185211513642314, "mime", false, 7974677327052559545], [10629569228670356391, "futures_util", false, 11209357034906847022], [11946729385090170470, "async_trait", false, 1054679854502272457], [11957360342995674422, "hyper", false, 5308235003974783957], [13645307863515715290, "serde_path_to_error", false, 18386061554227490758], [14084095096285906100, "http_body", false, 11748545772775035563], [15367738274754116744, "serde_json", false, 11668620674550090685], [16066129441945555748, "bytes", false, 6340292206522364871], [16542808166767769916, "serde_urlencoded", false, 2326710886261363035], [16900715236047033623, "http_body_util", false, 13122697805833452045]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\axum-ce9826dcda1dec22\\dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 16591470773350601817, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 3541797763817303166, "path": 9217284991197863351, "deps": [[1009387600818341822, "matchers", false, 13618301269287359027], [1017461770342116999, "sharded_slab", false, 6838058619723345657], [3722963349756955755, "once_cell", false, 13309921639290301992], [6048213226671835012, "smallvec", false, 18024043512637474914], [8606274917505247608, "tracing", false, 15596637546793430723], [8614575489689151157, "nu_ansi_term", false, 8617473672628189290], [9451456094439810778, "regex", false, 10279930515906905993], [10806489435541507125, "tracing_log", false, 2025267260805453978], [11033263105862272874, "tracing_core", false, 16762748058436349872], [12427285511609802057, "thread_local", false, 8935707107379240552]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tracing-subscriber-7c9d126b3c1c08ac\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
//! License validation and management for the VPN server

use ultrasecure_vpn_core::{
    VpnError, Result,
    licensing::{LicenseState, LicenseClaims},
};
use crate::config::LicensingConfig;
use sqlx::{SqlitePool, Row};
use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH};
use uuid::Uuid;
use tracing::{info, warn, debug, error};

/// License management system
pub struct LicenseManager {
    /// Configuration
    config: LicensingConfig,
    /// Database connection pool
    db_pool: SqlitePool,
    /// License cache (license_id -> cached validation)
    license_cache: tokio::sync::RwLock<HashMap<String, CachedLicense>>,
    /// Hardware fingerprint to license mapping
    hardware_to_license: tokio::sync::RwLock<HashMap<String, String>>,
}

/// Cached license information
#[derive(Debug, Clone)]
struct CachedLicense {
    claims: LicenseClaims,
    cached_at: u64,
    is_valid: bool,
}

impl LicenseManager {
    /// Create new license manager
    pub async fn new(config: LicensingConfig, db_pool: SqlitePool) -> Result<Self> {
        let manager = Self {
            config,
            db_pool,
            license_cache: tokio::sync::RwLock::new(HashMap::new()),
            hardware_to_license: tokio::sync::RwLock::new(HashMap::new()),
        };
        
        // Initialize database tables
        manager.init_database().await?;
        
        // Load existing licenses into cache
        manager.load_license_cache().await?;
        
        info!("📋 License manager initialized");
        Ok(manager)
    }
    
    /// Validate a license token
    pub async fn validate_license(
        &self,
        token: &str,
        hardware_fingerprint: &str,
    ) -> Result<LicenseState> {
        debug!("🔍 Validating license for hardware: {}", hardware_fingerprint);
        
        // Parse JWT token (simplified - in production use proper JWT validation)
        let claims = self.parse_license_token(token).await?;
        
        // Check cache first
        if let Some(cached) = self.get_cached_license(&claims.license_id).await {
            if cached.is_valid && !self.is_cache_expired(&cached) {
                debug!("✅ License found in cache: {}", claims.license_id);
                return Ok(LicenseState::validate(token.to_string()).await?);
            }
        }
        
        // Validate license claims
        self.validate_license_claims(&claims, hardware_fingerprint).await?;
        
        // Check database for revocation
        if self.is_license_revoked(&claims.license_id).await? {
            return Err(VpnError::LicenseError("License has been revoked".to_string()));
        }
        
        // Update cache
        self.cache_license(&claims, true).await;
        
        // Record license usage
        self.record_license_usage(&claims, hardware_fingerprint).await?;
        
        info!("✅ License validated: {} for {}", claims.license_id, hardware_fingerprint);
        
        // Create license state
        LicenseState::validate(token.to_string()).await
    }
    
    /// Parse license token (simplified JWT parsing)
    async fn parse_license_token(&self, token: &str) -> Result<LicenseClaims> {
        // In production, use proper JWT validation with signature verification
        let parts: Vec<&str> = token.split('.').collect();
        if parts.len() != 3 {
            return Err(VpnError::LicenseError("Invalid JWT format".to_string()));
        }
        
        // Decode payload
        let payload = base64::decode_config(parts[1], base64::URL_SAFE_NO_PAD)
            .map_err(|e| VpnError::LicenseError(format!("Invalid JWT payload: {}", e)))?;
        
        let claims: LicenseClaims = serde_json::from_slice(&payload)
            .map_err(|e| VpnError::LicenseError(format!("Invalid claims format: {}", e)))?;
        
        Ok(claims)
    }
    
    /// Validate license claims
    async fn validate_license_claims(
        &self,
        claims: &LicenseClaims,
        hardware_fingerprint: &str,
    ) -> Result<()> {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        // Check expiration
        if claims.expires_at < now {
            return Err(VpnError::LicenseError("License has expired".to_string()));
        }
        
        // Check hardware binding if enabled
        if self.config.validate_hardware_fingerprint {
            if claims.hardware_fingerprint != hardware_fingerprint {
                return Err(VpnError::LicenseError("License is bound to different hardware".to_string()));
            }
        }
        
        // Check minimum tier requirement
        if !self.is_tier_sufficient(&claims.tier) {
            return Err(VpnError::LicenseError("Insufficient license tier".to_string()));
        }
        
        // Check concurrent connections
        let active_connections = self.get_active_connections(&claims.license_id).await?;
        if active_connections >= claims.max_connections {
            return Err(VpnError::LicenseError("Maximum concurrent connections exceeded".to_string()));
        }
        
        Ok(())
    }
    
    /// Check if license tier is sufficient
    fn is_tier_sufficient(&self, tier: &str) -> bool {
        let tier_levels = ["basic", "premium", "enterprise"];
        let required_level = tier_levels.iter().position(|&t| t == self.config.min_license_tier.as_str()).unwrap_or(0);
        let user_level = tier_levels.iter().position(|&t| t == tier).unwrap_or(0);
        
        user_level >= required_level
    }
    
    /// Get cached license
    async fn get_cached_license(&self, license_id: &str) -> Option<CachedLicense> {
        let cache = self.license_cache.read().await;
        cache.get(license_id).cloned()
    }
    
    /// Check if cache entry is expired
    fn is_cache_expired(&self, cached: &CachedLicense) -> bool {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        now - cached.cached_at > self.config.cache_duration as u64
    }
    
    /// Cache license validation result
    async fn cache_license(&self, claims: &LicenseClaims, is_valid: bool) {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        let cached = CachedLicense {
            claims: claims.clone(),
            cached_at: now,
            is_valid,
        };
        
        let mut cache = self.license_cache.write().await;
        cache.insert(claims.license_id.clone(), cached);
        
        let mut hardware_map = self.hardware_to_license.write().await;
        hardware_map.insert(claims.hardware_fingerprint.clone(), claims.license_id.clone());
    }
    
    /// Check if license is revoked
    async fn is_license_revoked(&self, license_id: &str) -> Result<bool> {
        let row = sqlx::query("SELECT revoked FROM licenses WHERE license_id = ?")
            .bind(license_id)
            .fetch_optional(&self.db_pool)
            .await
            .map_err(|e| VpnError::LicenseError(format!("Database error: {}", e)))?;
        
        Ok(row.map(|r| r.get::<bool, _>("revoked")).unwrap_or(false))
    }
    
    /// Get number of active connections for a license
    async fn get_active_connections(&self, license_id: &str) -> Result<u32> {
        let row = sqlx::query("SELECT COUNT(*) as count FROM active_sessions WHERE license_id = ?")
            .bind(license_id)
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| VpnError::LicenseError(format!("Database error: {}", e)))?;
        
        Ok(row.get::<i64, _>("count") as u32)
    }
    
    /// Record license usage
    async fn record_license_usage(
        &self,
        claims: &LicenseClaims,
        hardware_fingerprint: &str,
    ) -> Result<()> {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        sqlx::query(
            "INSERT OR REPLACE INTO license_usage 
             (license_id, user_id, hardware_fingerprint, last_used, usage_count) 
             VALUES (?, ?, ?, ?, COALESCE((SELECT usage_count FROM license_usage WHERE license_id = ?) + 1, 1))"
        )
        .bind(&claims.license_id)
        .bind(&claims.user_id)
        .bind(hardware_fingerprint)
        .bind(now as i64)
        .bind(&claims.license_id)
        .execute(&self.db_pool)
        .await
        .map_err(|e| VpnError::LicenseError(format!("Failed to record usage: {}", e)))?;
        
        Ok(())
    }
    
    /// Initialize database tables
    async fn init_database(&self) -> Result<()> {
        // Create licenses table
        sqlx::query(
            "CREATE TABLE IF NOT EXISTS licenses (
                license_id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                hardware_fingerprint TEXT NOT NULL,
                tier TEXT NOT NULL,
                expires_at INTEGER NOT NULL,
                max_connections INTEGER NOT NULL,
                bandwidth_limit INTEGER NOT NULL,
                data_limit INTEGER NOT NULL,
                revoked BOOLEAN DEFAULT FALSE,
                created_at INTEGER NOT NULL
            )"
        )
        .execute(&self.db_pool)
        .await
        .map_err(|e| VpnError::LicenseError(format!("Failed to create licenses table: {}", e)))?;
        
        // Create license usage table
        sqlx::query(
            "CREATE TABLE IF NOT EXISTS license_usage (
                license_id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                hardware_fingerprint TEXT NOT NULL,
                last_used INTEGER NOT NULL,
                usage_count INTEGER NOT NULL,
                FOREIGN KEY (license_id) REFERENCES licenses (license_id)
            )"
        )
        .execute(&self.db_pool)
        .await
        .map_err(|e| VpnError::LicenseError(format!("Failed to create usage table: {}", e)))?;
        
        // Create active sessions table
        sqlx::query(
            "CREATE TABLE IF NOT EXISTS active_sessions (
                session_id TEXT PRIMARY KEY,
                license_id TEXT NOT NULL,
                client_ip TEXT NOT NULL,
                socket_addr TEXT NOT NULL,
                connected_at INTEGER NOT NULL,
                last_seen INTEGER NOT NULL,
                FOREIGN KEY (license_id) REFERENCES licenses (license_id)
            )"
        )
        .execute(&self.db_pool)
        .await
        .map_err(|e| VpnError::LicenseError(format!("Failed to create sessions table: {}", e)))?;
        
        debug!("📊 Database tables initialized");
        Ok(())
    }
    
    /// Load existing licenses into cache
    async fn load_license_cache(&self) -> Result<()> {
        let rows = sqlx::query("SELECT * FROM licenses WHERE NOT revoked")
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| VpnError::LicenseError(format!("Failed to load licenses: {}", e)))?;
        
        let mut cache = self.license_cache.write().await;
        let mut hardware_map = self.hardware_to_license.write().await;
        
        for row in rows {
            let license_id: String = row.get("license_id");
            let user_id: String = row.get("user_id");
            let hardware_fingerprint: String = row.get("hardware_fingerprint");
            let tier: String = row.get("tier");
            let expires_at: i64 = row.get("expires_at");
            let max_connections: i64 = row.get("max_connections");
            let bandwidth_limit: i64 = row.get("bandwidth_limit");
            let data_limit: i64 = row.get("data_limit");
            
            let claims = LicenseClaims {
                license_id: license_id.clone(),
                user_id,
                hardware_fingerprint: hardware_fingerprint.clone(),
                expires_at: expires_at as u64,
                issued_at: 0, // Not stored in DB for this example
                max_connections: max_connections as u32,
                features: vec![], // Not stored in DB for this example
                tier,
                bandwidth_limit: bandwidth_limit as u64,
                data_limit: data_limit as u64,
                allowed_regions: vec![], // Not stored in DB for this example
            };
            
            let cached = CachedLicense {
                claims,
                cached_at: SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs(),
                is_valid: true,
            };
            
            cache.insert(license_id.clone(), cached);
            hardware_map.insert(hardware_fingerprint, license_id);
        }
        
        info!("📋 Loaded {} licenses into cache", cache.len());
        Ok(())
    }
    
    /// Add session to active sessions tracking
    pub async fn add_active_session(
        &self,
        session_id: Uuid,
        license_id: &str,
        client_ip: &str,
        socket_addr: &str,
    ) -> Result<()> {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        sqlx::query(
            "INSERT INTO active_sessions 
             (session_id, license_id, client_ip, socket_addr, connected_at, last_seen) 
             VALUES (?, ?, ?, ?, ?, ?)"
        )
        .bind(session_id.to_string())
        .bind(license_id)
        .bind(client_ip)
        .bind(socket_addr)
        .bind(now as i64)
        .bind(now as i64)
        .execute(&self.db_pool)
        .await
        .map_err(|e| VpnError::LicenseError(format!("Failed to add session: {}", e)))?;
        
        Ok(())
    }
    
    /// Remove session from active sessions tracking
    pub async fn remove_active_session(&self, session_id: Uuid) -> Result<()> {
        sqlx::query("DELETE FROM active_sessions WHERE session_id = ?")
            .bind(session_id.to_string())
            .execute(&self.db_pool)
            .await
            .map_err(|e| VpnError::LicenseError(format!("Failed to remove session: {}", e)))?;
        
        Ok(())
    }
}

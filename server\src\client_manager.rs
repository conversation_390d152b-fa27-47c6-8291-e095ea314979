//! Client session management

use ultrasecure_vpn_core::{
    crypto::CryptoState,
    licensing::LicenseState,
    Result,
};
use std::collections::HashMap;
use std::net::{SocketAddr, IpAddr};
use std::time::{Instant, Duration};
use uuid::Uuid;
use tracing::{info, debug, warn};

/// Manages all client sessions
pub struct ClientManager {
    /// Active client sessions by session ID
    sessions: HashMap<Uuid, ClientSession>,
    /// Session lookup by socket address
    addr_to_session: HashMap<SocketAddr, Uuid>,
    /// Session lookup by assigned IP
    ip_to_session: HashMap<IpAddr, Uuid>,
    /// Maximum number of clients
    max_clients: u32,
    /// Session timeout duration
    session_timeout: Duration,
}

impl ClientManager {
    /// Create new client manager
    pub fn new(max_clients: u32) -> Self {
        Self {
            sessions: HashMap::new(),
            addr_to_session: HashMap::new(),
            ip_to_session: HashMap::new(),
            max_clients,
            session_timeout: Duration::from_secs(300), // 5 minutes
        }
    }
    
    /// Add new client session
    pub fn add_session(&mut self, session: ClientSession) {
        let session_id = session.session_id;
        let socket_addr = session.socket_addr;
        let client_ip = session.client_ip;
        
        // Remove any existing session for this address
        if let Some(old_session_id) = self.addr_to_session.get(&socket_addr) {
            self.remove_session(old_session_id);
        }
        
        // Add new session
        self.sessions.insert(session_id, session);
        self.addr_to_session.insert(socket_addr, session_id);
        self.ip_to_session.insert(client_ip, session_id);
        
        info!("➕ Added client session {} for {}", session_id, socket_addr);
    }
    
    /// Remove client session
    pub fn remove_session(&mut self, session_id: &Uuid) -> Option<ClientSession> {
        if let Some(session) = self.sessions.remove(session_id) {
            self.addr_to_session.remove(&session.socket_addr);
            self.ip_to_session.remove(&session.client_ip);
            
            info!("➖ Removed client session {} for {}", session_id, session.socket_addr);
            Some(session)
        } else {
            None
        }
    }
    
    /// Get session by ID
    pub fn get_session(&self, session_id: &Uuid) -> Option<&ClientSession> {
        self.sessions.get(session_id)
    }
    
    /// Get session by socket address
    pub fn get_session_by_addr(&self, addr: &SocketAddr) -> Option<ClientSession> {
        self.addr_to_session.get(addr)
            .and_then(|session_id| self.sessions.get(session_id))
            .cloned()
    }
    
    /// Get session by client IP
    pub fn get_session_by_ip(&self, ip: &IpAddr) -> Option<&ClientSession> {
        self.ip_to_session.get(ip)
            .and_then(|session_id| self.sessions.get(session_id))
    }
    
    /// Update last seen time for a session
    pub fn update_last_seen(&mut self, session_id: &Uuid) {
        if let Some(session) = self.sessions.get_mut(session_id) {
            session.last_seen = Instant::now();
            debug!("🕐 Updated last seen for session {}", session_id);
        }
    }
    
    /// Get number of active clients
    pub fn get_client_count(&self) -> u32 {
        self.sessions.len() as u32
    }
    
    /// Check if at capacity
    pub fn is_at_capacity(&self) -> bool {
        self.get_client_count() >= self.max_clients
    }
    
    /// Clean up expired sessions
    pub fn cleanup_expired_sessions(&mut self) {
        let now = Instant::now();
        let expired_sessions: Vec<Uuid> = self.sessions
            .iter()
            .filter(|(_, session)| now.duration_since(session.last_seen) > self.session_timeout)
            .map(|(id, _)| *id)
            .collect();
        
        for session_id in expired_sessions {
            warn!("⏰ Session {} expired, removing", session_id);
            self.remove_session(&session_id);
        }
    }
    
    /// Disconnect all clients
    pub async fn disconnect_all(&mut self) {
        let session_ids: Vec<Uuid> = self.sessions.keys().cloned().collect();
        
        for session_id in session_ids {
            self.remove_session(&session_id);
        }
        
        info!("🔌 Disconnected all clients");
    }
    
    /// Get all active sessions
    pub fn get_all_sessions(&self) -> Vec<&ClientSession> {
        self.sessions.values().collect()
    }
    
    /// Get session statistics
    pub fn get_statistics(&self) -> ClientManagerStats {
        let total_sessions = self.sessions.len();
        let mut by_license_tier = HashMap::new();
        let mut total_bandwidth_used = 0u64;
        
        for session in self.sessions.values() {
            let tier = &session.license_state.get_claims().tier;
            *by_license_tier.entry(tier.clone()).or_insert(0) += 1;
            total_bandwidth_used += session.bandwidth_monitor.get_total_bytes().0;
            total_bandwidth_used += session.bandwidth_monitor.get_total_bytes().1;
        }
        
        ClientManagerStats {
            total_sessions,
            max_sessions: self.max_clients as usize,
            sessions_by_tier: by_license_tier,
            total_bandwidth_used,
        }
    }
}

/// Individual client session
#[derive(Debug, Clone)]
pub struct ClientSession {
    /// Unique session identifier
    pub session_id: Uuid,
    /// Client's socket address
    pub socket_addr: SocketAddr,
    /// Assigned VPN IP address
    pub client_ip: IpAddr,
    /// License validation state
    pub license_state: LicenseState,
    /// Cryptographic state for this session
    pub crypto_state: CryptoState,
    /// Last activity timestamp
    pub last_seen: Instant,
    /// Session creation time
    pub created_at: Instant,
    /// Bandwidth monitoring
    pub bandwidth_monitor: BandwidthMonitor,
    /// Connection statistics
    pub stats: SessionStats,
}

impl ClientSession {
    /// Create new client session
    pub fn new(
        session_id: Uuid,
        socket_addr: SocketAddr,
        client_ip: IpAddr,
        license_state: LicenseState,
        crypto_state: CryptoState,
    ) -> Self {
        let now = Instant::now();
        
        Self {
            session_id,
            socket_addr,
            client_ip,
            license_state,
            crypto_state,
            last_seen: now,
            created_at: now,
            bandwidth_monitor: BandwidthMonitor::new(),
            stats: SessionStats::new(),
        }
    }
    
    /// Check if session is expired
    pub fn is_expired(&self, timeout: Duration) -> bool {
        Instant::now().duration_since(self.last_seen) > timeout
    }
    
    /// Get session duration
    pub fn get_duration(&self) -> Duration {
        Instant::now().duration_since(self.created_at)
    }
    
    /// Update bandwidth usage
    pub fn record_bandwidth(&mut self, bytes_sent: u64, bytes_received: u64) {
        self.bandwidth_monitor.record_sent(bytes_sent);
        self.bandwidth_monitor.record_received(bytes_received);
        self.stats.total_bytes_sent += bytes_sent;
        self.stats.total_bytes_received += bytes_received;
    }
    
    /// Check if bandwidth limit is exceeded
    pub fn is_bandwidth_limit_exceeded(&self) -> bool {
        let limit = self.license_state.get_bandwidth_limit();
        if limit == 0 {
            return false; // Unlimited
        }
        
        let current_speed = self.bandwidth_monitor.get_upload_speed() + 
                           self.bandwidth_monitor.get_download_speed();
        current_speed > limit as f64
    }
}

/// Bandwidth monitoring for individual sessions
#[derive(Debug, Clone)]
pub struct BandwidthMonitor {
    bytes_sent: u64,
    bytes_received: u64,
    start_time: Instant,
    last_reset: Instant,
}

impl BandwidthMonitor {
    pub fn new() -> Self {
        let now = Instant::now();
        Self {
            bytes_sent: 0,
            bytes_received: 0,
            start_time: now,
            last_reset: now,
        }
    }
    
    pub fn record_sent(&mut self, bytes: u64) {
        self.bytes_sent += bytes;
    }
    
    pub fn record_received(&mut self, bytes: u64) {
        self.bytes_received += bytes;
    }
    
    pub fn get_upload_speed(&self) -> f64 {
        let elapsed = self.last_reset.elapsed().as_secs_f64();
        if elapsed > 0.0 {
            self.bytes_sent as f64 / elapsed
        } else {
            0.0
        }
    }
    
    pub fn get_download_speed(&self) -> f64 {
        let elapsed = self.last_reset.elapsed().as_secs_f64();
        if elapsed > 0.0 {
            self.bytes_received as f64 / elapsed
        } else {
            0.0
        }
    }
    
    pub fn get_total_bytes(&self) -> (u64, u64) {
        (self.bytes_sent, self.bytes_received)
    }
    
    pub fn reset(&mut self) {
        self.bytes_sent = 0;
        self.bytes_received = 0;
        self.last_reset = Instant::now();
    }
}

/// Session statistics
#[derive(Debug, Clone)]
pub struct SessionStats {
    pub total_bytes_sent: u64,
    pub total_bytes_received: u64,
    pub packets_sent: u64,
    pub packets_received: u64,
    pub connection_errors: u32,
    pub last_error: Option<String>,
}

impl SessionStats {
    pub fn new() -> Self {
        Self {
            total_bytes_sent: 0,
            total_bytes_received: 0,
            packets_sent: 0,
            packets_received: 0,
            connection_errors: 0,
            last_error: None,
        }
    }
    
    pub fn record_packet_sent(&mut self, bytes: u64) {
        self.packets_sent += 1;
        self.total_bytes_sent += bytes;
    }
    
    pub fn record_packet_received(&mut self, bytes: u64) {
        self.packets_received += 1;
        self.total_bytes_received += bytes;
    }
    
    pub fn record_error(&mut self, error: String) {
        self.connection_errors += 1;
        self.last_error = Some(error);
    }
}

/// Client manager statistics
#[derive(Debug)]
pub struct ClientManagerStats {
    pub total_sessions: usize,
    pub max_sessions: usize,
    pub sessions_by_tier: HashMap<String, usize>,
    pub total_bandwidth_used: u64,
}

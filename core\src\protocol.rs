//! Custom VPN protocol definitions
//! 
//! This module defines our proprietary VPN protocol that includes:
//! - Secure key exchange
//! - License validation
//! - Data transmission
//! - Connection management

use serde::{Deserialize, Serialize};
use x25519_dalek::PublicKey;
use ed25519_dalek::Signature;
use std::net::IpAddr;
use uuid::Uuid;

/// Protocol version
pub const PROTOCOL_VERSION: u16 = 1;

/// Maximum packet size
pub const MAX_PACKET_SIZE: usize = 65536;

/// Key exchange packet sent by client to server
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct KeyExchange {
    /// Protocol version
    pub version: u16,
    /// Client's ephemeral public key for ECDH
    pub client_public_key: [u8; 32],
    /// <PERSON>lient's license token
    pub license_token: String,
    /// Client's hardware fingerprint
    pub hardware_fingerprint: String,
    /// Timestamp to prevent replay attacks
    pub timestamp: u64,
    /// Random nonce
    pub nonce: [u8; 32],
}

impl KeyExchange {
    pub fn new(public_key: PublicKey, license_token: String) -> Self {
        use rand::RngCore;
        let mut nonce = [0u8; 32];
        rand::rngs::OsRng.fill_bytes(&mut nonce);
        
        Self {
            version: PROTOCOL_VERSION,
            client_public_key: *public_key.as_bytes(),
            license_token,
            hardware_fingerprint: crate::licensing::get_hardware_fingerprint(),
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            nonce,
        }
    }
}

/// Key exchange response from server
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct KeyExchangeResponse {
    /// Protocol version
    pub version: u16,
    /// Server's ephemeral public key for ECDH
    pub server_public_key: [u8; 32],
    /// Session ID for this connection
    pub session_id: Uuid,
    /// Assigned client IP address
    pub client_ip: IpAddr,
    /// Server's signature of the exchange
    pub signature: [u8; 64],
    /// Connection parameters
    pub connection_params: ConnectionParams,
    /// Timestamp
    pub timestamp: u64,
}

/// Connection parameters negotiated during handshake
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ConnectionParams {
    /// MTU size for the tunnel
    pub mtu: u16,
    /// Keep-alive interval in seconds
    pub keepalive_interval: u32,
    /// Key rotation interval in seconds
    pub key_rotation_interval: u32,
    /// Compression enabled
    pub compression: bool,
    /// Obfuscation mode
    pub obfuscation_mode: ObfuscationMode,
}

impl Default for ConnectionParams {
    fn default() -> Self {
        Self {
            mtu: 1420,
            keepalive_interval: 30,
            key_rotation_interval: 3600, // 1 hour
            compression: true,
            obfuscation_mode: ObfuscationMode::HttpsLike,
        }
    }
}

/// Traffic obfuscation modes
#[derive(Serialize, Deserialize, Debug, Clone, Copy)]
pub enum ObfuscationMode {
    /// No obfuscation
    None,
    /// Make traffic look like HTTPS
    HttpsLike,
    /// Make traffic look like HTTP/2
    Http2Like,
    /// Custom steganographic mode
    Steganographic,
}

/// Main VPN packet types
#[derive(Serialize, Deserialize, Debug, Clone)]
pub enum VpnPacket {
    /// Data packet containing tunneled traffic
    Data(DataPacket),
    /// Keep-alive packet
    KeepAlive,
    /// Key rotation request
    KeyRotation(KeyRotationPacket),
    /// Disconnect notification
    Disconnect,
    /// License renewal
    LicenseRenewal(LicenseRenewalPacket),
}

/// Data packet for tunneled traffic
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct DataPacket {
    /// Sequence number for ordering
    pub sequence: u64,
    /// Payload data (IP packet)
    pub payload: Vec<u8>,
    /// Timestamp for latency measurement
    pub timestamp: u64,
    /// Checksum for integrity
    pub checksum: u32,
}

impl DataPacket {
    pub fn new(sequence: u64, payload: Vec<u8>) -> Self {
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;
        
        // Simple checksum (CRC32 would be better in production)
        let checksum = crc32fast::hash(&payload);
        
        Self {
            sequence,
            payload,
            timestamp,
            checksum,
        }
    }
    
    /// Verify packet integrity
    pub fn verify_checksum(&self) -> bool {
        crc32fast::hash(&self.payload) == self.checksum
    }
}

/// Key rotation packet
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct KeyRotationPacket {
    /// New ephemeral public key
    pub new_public_key: [u8; 32],
    /// Rotation sequence number
    pub rotation_sequence: u64,
    /// Timestamp
    pub timestamp: u64,
    /// Signature of the rotation request
    pub signature: [u8; 64],
}

/// License renewal packet
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct LicenseRenewalPacket {
    /// New license token
    pub new_license_token: String,
    /// Expiration time
    pub expires_at: u64,
    /// Hardware fingerprint
    pub hardware_fingerprint: String,
    /// Signature
    pub signature: [u8; 64],
}

/// Error codes for protocol errors
#[derive(Serialize, Deserialize, Debug, Clone, Copy)]
pub enum ProtocolError {
    /// Invalid protocol version
    InvalidVersion,
    /// License validation failed
    LicenseInvalid,
    /// License expired
    LicenseExpired,
    /// Hardware fingerprint mismatch
    HardwareMismatch,
    /// Invalid signature
    InvalidSignature,
    /// Replay attack detected
    ReplayAttack,
    /// Rate limit exceeded
    RateLimitExceeded,
    /// Server overloaded
    ServerOverloaded,
    /// Unknown error
    Unknown,
}

/// Protocol error response
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ErrorResponse {
    /// Error code
    pub error: ProtocolError,
    /// Human-readable error message
    pub message: String,
    /// Timestamp
    pub timestamp: u64,
}

impl ErrorResponse {
    pub fn new(error: ProtocolError, message: String) -> Self {
        Self {
            error,
            message,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        }
    }
}

/// Packet header for all protocol messages
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct PacketHeader {
    /// Magic bytes for packet identification
    pub magic: [u8; 4],
    /// Packet type identifier
    pub packet_type: u8,
    /// Packet length
    pub length: u32,
    /// Session ID
    pub session_id: Option<Uuid>,
    /// Sequence number
    pub sequence: u64,
}

impl PacketHeader {
    /// Magic bytes for our protocol
    pub const MAGIC: [u8; 4] = [0x55, 0x53, 0x56, 0x50]; // "USVP"
    
    pub fn new(packet_type: u8, length: u32, session_id: Option<Uuid>, sequence: u64) -> Self {
        Self {
            magic: Self::MAGIC,
            packet_type,
            length,
            session_id,
            sequence,
        }
    }
    
    /// Verify magic bytes
    pub fn is_valid(&self) -> bool {
        self.magic == Self::MAGIC
    }
}

/// Packet types
pub mod packet_types {
    pub const KEY_EXCHANGE: u8 = 0x01;
    pub const KEY_EXCHANGE_RESPONSE: u8 = 0x02;
    pub const DATA: u8 = 0x03;
    pub const KEEP_ALIVE: u8 = 0x04;
    pub const KEY_ROTATION: u8 = 0x05;
    pub const DISCONNECT: u8 = 0x06;
    pub const LICENSE_RENEWAL: u8 = 0x07;
    pub const ERROR: u8 = 0xFF;
}

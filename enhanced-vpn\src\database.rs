//! Database management for Enhanced VPN

use rusqlite::{<PERSON>, params, Result as SqlResult};
use std::sync::Arc;
use tokio::sync::Mutex;
use anyhow::{Result, anyhow};
use serde::{Deserialize, Serialize};
use std::time::{SystemTime, UNIX_EPOCH};

#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct LicenseRecord {
    pub license_id: String,
    pub user_id: String,
    pub hardware_fingerprint: String,
    pub tier: String,
    pub expires_at: u64,
    pub max_connections: u32,
    pub bandwidth_limit: u64,
    pub data_limit: u64,
    pub is_active: bool,
    pub created_at: u64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ConnectionRecord {
    pub session_id: String,
    pub license_id: String,
    pub client_ip: String,
    pub server_ip: String,
    pub connected_at: u64,
    pub disconnected_at: Option<u64>,
    pub bytes_sent: u64,
    pub bytes_received: u64,
    pub duration_seconds: u64,
}

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>ize, Deserialize)]
pub struct UsageStats {
    pub total_connections: u64,
    pub active_connections: u64,
    pub total_data_transferred: u64,
    pub unique_users: u64,
    pub average_session_duration: f64,
}

pub struct Database {
    conn: Arc<Mutex<Connection>>,
}

impl Database {
    pub async fn new(db_path: &str) -> Result<Self> {
        let conn = Connection::open(db_path)?;
        let db = Self {
            conn: Arc::new(Mutex::new(conn)),
        };
        
        db.initialize_tables().await?;
        Ok(db)
    }
    
    async fn initialize_tables(&self) -> Result<()> {
        let conn = self.conn.lock().await;
        
        // Licenses table
        conn.execute(
            "CREATE TABLE IF NOT EXISTS licenses (
                license_id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                hardware_fingerprint TEXT NOT NULL,
                tier TEXT NOT NULL,
                expires_at INTEGER NOT NULL,
                max_connections INTEGER NOT NULL,
                bandwidth_limit INTEGER NOT NULL,
                data_limit INTEGER NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at INTEGER NOT NULL,
                updated_at INTEGER DEFAULT CURRENT_TIMESTAMP
            )",
            [],
        )?;
        
        // Connections table
        conn.execute(
            "CREATE TABLE IF NOT EXISTS connections (
                session_id TEXT PRIMARY KEY,
                license_id TEXT NOT NULL,
                client_ip TEXT NOT NULL,
                server_ip TEXT NOT NULL,
                connected_at INTEGER NOT NULL,
                disconnected_at INTEGER,
                bytes_sent INTEGER DEFAULT 0,
                bytes_received INTEGER DEFAULT 0,
                duration_seconds INTEGER DEFAULT 0,
                FOREIGN KEY (license_id) REFERENCES licenses (license_id)
            )",
            [],
        )?;
        
        // Usage statistics table
        conn.execute(
            "CREATE TABLE IF NOT EXISTS usage_stats (
                date TEXT PRIMARY KEY,
                total_connections INTEGER DEFAULT 0,
                unique_users INTEGER DEFAULT 0,
                total_data_transferred INTEGER DEFAULT 0,
                average_session_duration REAL DEFAULT 0.0,
                created_at INTEGER DEFAULT CURRENT_TIMESTAMP
            )",
            [],
        )?;
        
        // Blocked IPs table
        conn.execute(
            "CREATE TABLE IF NOT EXISTS blocked_ips (
                ip_address TEXT PRIMARY KEY,
                reason TEXT NOT NULL,
                blocked_at INTEGER NOT NULL,
                expires_at INTEGER,
                is_permanent BOOLEAN DEFAULT FALSE
            )",
            [],
        )?;
        
        // Audit log table
        conn.execute(
            "CREATE TABLE IF NOT EXISTS audit_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                event_type TEXT NOT NULL,
                user_id TEXT,
                license_id TEXT,
                ip_address TEXT,
                details TEXT,
                timestamp INTEGER NOT NULL
            )",
            [],
        )?;
        
        Ok(())
    }
    
    pub async fn validate_license(&self, license_key: &str, hardware_fingerprint: &str) -> Result<bool> {
        let conn = self.conn.lock().await;
        
        // Decode license key (simplified - in production use proper JWT validation)
        let decoded = base64::decode(license_key)
            .map_err(|e| anyhow!("Invalid license format: {}", e))?;
        
        let license_data: serde_json::Value = serde_json::from_slice(&decoded)
            .map_err(|e| anyhow!("Invalid license data: {}", e))?;
        
        let license_id = license_data["license_id"].as_str()
            .ok_or_else(|| anyhow!("Missing license ID"))?;
        
        // Check if license exists and is valid
        let mut stmt = conn.prepare(
            "SELECT expires_at, hardware_fingerprint, is_active, max_connections 
             FROM licenses WHERE license_id = ?"
        )?;
        
        let license_info: Option<(u64, String, bool, u32)> = stmt.query_row(
            params![license_id],
            |row| Ok((
                row.get(0)?,
                row.get(1)?,
                row.get(2)?,
                row.get(3)?,
            ))
        ).optional()?;
        
        if let Some((expires_at, stored_fingerprint, is_active, max_connections)) = license_info {
            let now = SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs();
            
            // Check if license is active and not expired
            if !is_active || expires_at < now {
                self.log_audit_event("LICENSE_VALIDATION_FAILED", None, Some(license_id), None, "License expired or inactive").await?;
                return Ok(false);
            }
            
            // Check hardware fingerprint
            if stored_fingerprint != hardware_fingerprint {
                self.log_audit_event("LICENSE_VALIDATION_FAILED", None, Some(license_id), None, "Hardware fingerprint mismatch").await?;
                return Ok(false);
            }
            
            // Check concurrent connections
            let active_connections = self.get_active_connections_for_license(license_id).await?;
            if active_connections >= max_connections {
                self.log_audit_event("LICENSE_VALIDATION_FAILED", None, Some(license_id), None, "Max connections exceeded").await?;
                return Ok(false);
            }
            
            self.log_audit_event("LICENSE_VALIDATION_SUCCESS", None, Some(license_id), None, "License validated successfully").await?;
            Ok(true)
        } else {
            self.log_audit_event("LICENSE_VALIDATION_FAILED", None, Some(license_id), None, "License not found").await?;
            Ok(false)
        }
    }
    
    pub async fn record_connection(&self, session_id: &str, client_ip: &str, assigned_ip: &str) -> Result<()> {
        let conn = self.conn.lock().await;
        let now = SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs();
        
        conn.execute(
            "INSERT INTO connections (session_id, license_id, client_ip, server_ip, connected_at)
             VALUES (?1, ?2, ?3, ?4, ?5)",
            params![session_id, "unknown", client_ip, assigned_ip, now],
        )?;
        
        Ok(())
    }
    
    pub async fn update_connection_stats(&self, session_id: &str, bytes_sent: u64, bytes_received: u64) -> Result<()> {
        let conn = self.conn.lock().await;
        
        conn.execute(
            "UPDATE connections SET bytes_sent = ?1, bytes_received = ?2 WHERE session_id = ?3",
            params![bytes_sent, bytes_received, session_id],
        )?;
        
        Ok(())
    }
    
    pub async fn close_connection(&self, session_id: &str) -> Result<()> {
        let conn = self.conn.lock().await;
        let now = SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs();
        
        // Calculate duration
        let mut stmt = conn.prepare("SELECT connected_at FROM connections WHERE session_id = ?")?;
        let connected_at: u64 = stmt.query_row(params![session_id], |row| row.get(0))?;
        let duration = now - connected_at;
        
        conn.execute(
            "UPDATE connections SET disconnected_at = ?1, duration_seconds = ?2 WHERE session_id = ?3",
            params![now, duration, session_id],
        )?;
        
        Ok(())
    }
    
    pub async fn get_active_connections_for_license(&self, license_id: &str) -> Result<u32> {
        let conn = self.conn.lock().await;
        
        let mut stmt = conn.prepare(
            "SELECT COUNT(*) FROM connections WHERE license_id = ? AND disconnected_at IS NULL"
        )?;
        
        let count: u32 = stmt.query_row(params![license_id], |row| row.get(0))?;
        Ok(count)
    }
    
    pub async fn get_usage_stats(&self) -> Result<UsageStats> {
        let conn = self.conn.lock().await;
        
        let total_connections: u64 = conn.query_row(
            "SELECT COUNT(*) FROM connections",
            [],
            |row| row.get(0)
        )?;
        
        let active_connections: u64 = conn.query_row(
            "SELECT COUNT(*) FROM connections WHERE disconnected_at IS NULL",
            [],
            |row| row.get(0)
        )?;
        
        let total_data_transferred: u64 = conn.query_row(
            "SELECT COALESCE(SUM(bytes_sent + bytes_received), 0) FROM connections",
            [],
            |row| row.get(0)
        )?;
        
        let unique_users: u64 = conn.query_row(
            "SELECT COUNT(DISTINCT license_id) FROM connections",
            [],
            |row| row.get(0)
        )?;
        
        let average_session_duration: f64 = conn.query_row(
            "SELECT COALESCE(AVG(duration_seconds), 0.0) FROM connections WHERE disconnected_at IS NOT NULL",
            [],
            |row| row.get(0)
        )?;
        
        Ok(UsageStats {
            total_connections,
            active_connections,
            total_data_transferred,
            unique_users,
            average_session_duration,
        })
    }
    
    pub async fn get_recent_connections(&self, limit: u32) -> Result<Vec<ConnectionRecord>> {
        let conn = self.conn.lock().await;
        
        let mut stmt = conn.prepare(
            "SELECT session_id, license_id, client_ip, server_ip, connected_at, 
                    disconnected_at, bytes_sent, bytes_received, duration_seconds
             FROM connections 
             ORDER BY connected_at DESC 
             LIMIT ?"
        )?;
        
        let rows = stmt.query_map(params![limit], |row| {
            Ok(ConnectionRecord {
                session_id: row.get(0)?,
                license_id: row.get(1)?,
                client_ip: row.get(2)?,
                server_ip: row.get(3)?,
                connected_at: row.get(4)?,
                disconnected_at: row.get(5)?,
                bytes_sent: row.get(6)?,
                bytes_received: row.get(7)?,
                duration_seconds: row.get(8)?,
            })
        })?;
        
        let mut connections = Vec::new();
        for row in rows {
            connections.push(row?);
        }
        
        Ok(connections)
    }
    
    pub async fn add_license(&self, license: LicenseRecord) -> Result<()> {
        let conn = self.conn.lock().await;
        
        conn.execute(
            "INSERT INTO licenses (license_id, user_id, hardware_fingerprint, tier, 
                                 expires_at, max_connections, bandwidth_limit, data_limit, 
                                 is_active, created_at)
             VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10)",
            params![
                license.license_id,
                license.user_id,
                license.hardware_fingerprint,
                license.tier,
                license.expires_at,
                license.max_connections,
                license.bandwidth_limit,
                license.data_limit,
                license.is_active,
                license.created_at,
            ],
        )?;
        
        self.log_audit_event("LICENSE_CREATED", Some(&license.user_id), Some(&license.license_id), None, "License created").await?;
        Ok(())
    }
    
    pub async fn revoke_license(&self, license_id: &str) -> Result<()> {
        let conn = self.conn.lock().await;
        
        conn.execute(
            "UPDATE licenses SET is_active = FALSE WHERE license_id = ?",
            params![license_id],
        )?;
        
        self.log_audit_event("LICENSE_REVOKED", None, Some(license_id), None, "License revoked").await?;
        Ok(())
    }
    
    pub async fn block_ip(&self, ip_address: &str, reason: &str, duration_hours: Option<u32>) -> Result<()> {
        let conn = self.conn.lock().await;
        let now = SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs();
        let expires_at = duration_hours.map(|hours| now + (hours as u64 * 3600));
        
        conn.execute(
            "INSERT OR REPLACE INTO blocked_ips (ip_address, reason, blocked_at, expires_at, is_permanent)
             VALUES (?1, ?2, ?3, ?4, ?5)",
            params![ip_address, reason, now, expires_at, duration_hours.is_none()],
        )?;
        
        self.log_audit_event("IP_BLOCKED", None, None, Some(ip_address), reason).await?;
        Ok(())
    }
    
    pub async fn is_ip_blocked(&self, ip_address: &str) -> Result<bool> {
        let conn = self.conn.lock().await;
        let now = SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs();
        
        let blocked: Option<bool> = conn.query_row(
            "SELECT TRUE FROM blocked_ips 
             WHERE ip_address = ? AND (expires_at IS NULL OR expires_at > ?)",
            params![ip_address, now],
            |_| Ok(true)
        ).optional()?;
        
        Ok(blocked.unwrap_or(false))
    }
    
    async fn log_audit_event(&self, event_type: &str, user_id: Option<&str>, license_id: Option<&str>, ip_address: Option<&str>, details: &str) -> Result<()> {
        let conn = self.conn.lock().await;
        let now = SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs();
        
        conn.execute(
            "INSERT INTO audit_log (event_type, user_id, license_id, ip_address, details, timestamp)
             VALUES (?1, ?2, ?3, ?4, ?5, ?6)",
            params![event_type, user_id, license_id, ip_address, details, now],
        )?;
        
        Ok(())
    }
    
    pub async fn get_audit_log(&self, limit: u32) -> Result<Vec<serde_json::Value>> {
        let conn = self.conn.lock().await;
        
        let mut stmt = conn.prepare(
            "SELECT event_type, user_id, license_id, ip_address, details, timestamp
             FROM audit_log 
             ORDER BY timestamp DESC 
             LIMIT ?"
        )?;
        
        let rows = stmt.query_map(params![limit], |row| {
            Ok(serde_json::json!({
                "event_type": row.get::<_, String>(0)?,
                "user_id": row.get::<_, Option<String>>(1)?,
                "license_id": row.get::<_, Option<String>>(2)?,
                "ip_address": row.get::<_, Option<String>>(3)?,
                "details": row.get::<_, String>(4)?,
                "timestamp": row.get::<_, u64>(5)?,
            }))
        })?;
        
        let mut events = Vec::new();
        for row in rows {
            events.push(row?);
        }
        
        Ok(events)
    }
}

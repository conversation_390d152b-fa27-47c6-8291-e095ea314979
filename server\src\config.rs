//! Server configuration management

use serde::{Deserialize, Serialize};
use std::net::{IpAddr, Ipv4Addr};
use ultrasecure_vpn_core::{VpnError, Result};

/// Server configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
    /// Network configuration
    pub network: NetworkConfig,
    
    /// Security configuration
    pub security: SecurityConfig,
    
    /// License server configuration
    pub licensing: LicensingConfig,
    
    /// Performance tuning
    pub performance: PerformanceConfig,
    
    /// Logging configuration
    pub logging: LoggingConfig,
}

/// Network configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkConfig {
    /// VPN subnet to assign to clients
    pub vpn_subnet: String,
    
    /// DNS servers to provide to clients
    pub dns_servers: Vec<IpAddr>,
    
    /// MTU size for the tunnel
    pub mtu: u16,
    
    /// Keep-alive interval in seconds
    pub keepalive_interval: u32,
    
    /// Maximum number of concurrent clients
    pub max_clients: u32,
    
    /// Port range for client connections
    pub port_range: (u16, u16),
}

impl Default for NetworkConfig {
    fn default() -> Self {
        Self {
            vpn_subnet: "********/24".to_string(),
            dns_servers: vec![
                IpAddr::V4(Ipv4Addr::new(1, 1, 1, 1)),    // Cloudflare
                IpAddr::V4(Ipv4Addr::new(8, 8, 8, 8)),    // Google
            ],
            mtu: 1420,
            keepalive_interval: 30,
            max_clients: 1000,
            port_range: (51821, 52000),
        }
    }
}

/// Security configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    /// Key rotation interval in seconds
    pub key_rotation_interval: u32,
    
    /// Maximum failed authentication attempts
    pub max_auth_failures: u32,
    
    /// Ban duration for failed attempts (seconds)
    pub ban_duration: u32,
    
    /// Enable traffic obfuscation
    pub enable_obfuscation: bool,
    
    /// Obfuscation mode
    pub obfuscation_mode: String,
    
    /// Enable perfect forward secrecy
    pub enable_pfs: bool,
    
    /// Minimum license tier required
    pub min_license_tier: String,
    
    /// Enable hardware fingerprint validation
    pub validate_hardware_fingerprint: bool,
}

impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            key_rotation_interval: 3600, // 1 hour
            max_auth_failures: 5,
            ban_duration: 300, // 5 minutes
            enable_obfuscation: true,
            obfuscation_mode: "https_like".to_string(),
            enable_pfs: true,
            min_license_tier: "basic".to_string(),
            validate_hardware_fingerprint: true,
        }
    }
}

/// Licensing configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LicensingConfig {
    /// License validation endpoint
    pub validation_endpoint: Option<String>,
    
    /// License signing key (for JWT validation)
    pub signing_key: String,
    
    /// Enable offline license validation
    pub enable_offline_validation: bool,
    
    /// License cache duration in seconds
    pub cache_duration: u32,
    
    /// Enable license revocation checking
    pub check_revocation: bool,
    
    /// Grace period for expired licenses (seconds)
    pub grace_period: u32,
}

impl Default for LicensingConfig {
    fn default() -> Self {
        Self {
            validation_endpoint: None,
            signing_key: "your-secret-key-here".to_string(),
            enable_offline_validation: true,
            cache_duration: 3600, // 1 hour
            check_revocation: false, // Disabled for budget deployment
            grace_period: 86400, // 24 hours
        }
    }
}

/// Performance configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    /// Number of worker threads
    pub worker_threads: Option<usize>,
    
    /// Buffer size for packet processing
    pub buffer_size: usize,
    
    /// Enable packet compression
    pub enable_compression: bool,
    
    /// Compression level (1-9)
    pub compression_level: u32,
    
    /// Enable bandwidth limiting
    pub enable_bandwidth_limiting: bool,
    
    /// Default bandwidth limit per client (bytes/sec)
    pub default_bandwidth_limit: u64,
    
    /// Enable connection pooling
    pub enable_connection_pooling: bool,
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            worker_threads: None, // Use default (number of CPU cores)
            buffer_size: 65536,
            enable_compression: true,
            compression_level: 6,
            enable_bandwidth_limiting: false,
            default_bandwidth_limit: 0, // Unlimited
            enable_connection_pooling: true,
        }
    }
}

/// Logging configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    /// Log level
    pub level: String,
    
    /// Log to file
    pub log_to_file: bool,
    
    /// Log file path
    pub log_file: String,
    
    /// Enable access logging
    pub enable_access_log: bool,
    
    /// Access log file path
    pub access_log_file: String,
    
    /// Log rotation size in MB
    pub rotation_size_mb: u64,
    
    /// Number of log files to keep
    pub keep_files: u32,
}

impl Default for LoggingConfig {
    fn default() -> Self {
        Self {
            level: "info".to_string(),
            log_to_file: true,
            log_file: "vpn_server.log".to_string(),
            enable_access_log: true,
            access_log_file: "access.log".to_string(),
            rotation_size_mb: 100,
            keep_files: 10,
        }
    }
}

impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            network: NetworkConfig::default(),
            security: SecurityConfig::default(),
            licensing: LicensingConfig::default(),
            performance: PerformanceConfig::default(),
            logging: LoggingConfig::default(),
        }
    }
}

impl ServerConfig {
    /// Load configuration from file
    pub async fn load(path: &str) -> Result<Self> {
        match tokio::fs::read_to_string(path).await {
            Ok(content) => {
                toml::from_str(&content)
                    .map_err(|e| VpnError::ConfigError(format!("Failed to parse config: {}", e)))
            }
            Err(_) => {
                // Create default config if file doesn't exist
                let default_config = Self::default();
                default_config.save(path).await?;
                Ok(default_config)
            }
        }
    }
    
    /// Save configuration to file
    pub async fn save(&self, path: &str) -> Result<()> {
        let content = toml::to_string_pretty(self)
            .map_err(|e| VpnError::ConfigError(format!("Failed to serialize config: {}", e)))?;
        
        tokio::fs::write(path, content).await
            .map_err(|e| VpnError::ConfigError(format!("Failed to write config: {}", e)))?;
        
        Ok(())
    }
    
    /// Validate configuration
    pub fn validate(&self) -> Result<()> {
        // Validate network configuration
        if self.network.max_clients == 0 {
            return Err(VpnError::ConfigError("max_clients must be greater than 0".to_string()));
        }
        
        if self.network.mtu < 576 || self.network.mtu > 9000 {
            return Err(VpnError::ConfigError("MTU must be between 576 and 9000".to_string()));
        }
        
        // Validate security configuration
        if self.security.key_rotation_interval < 300 {
            return Err(VpnError::ConfigError("key_rotation_interval must be at least 300 seconds".to_string()));
        }
        
        // Validate performance configuration
        if self.performance.compression_level > 9 {
            return Err(VpnError::ConfigError("compression_level must be between 1 and 9".to_string()));
        }
        
        Ok(())
    }
}

{"rustc": 16591470773350601817, "features": "[\"default\"]", "declared_features": "[\"debug\", \"default\", \"deprecated\", \"raw-deprecated\", \"unstable-markdown\", \"unstable-v5\"]", "target": 905583280159225126, "profile": 9476526975381666521, "path": 13225161651555737026, "deps": [[3060637413840920116, "proc_macro2", false, 6844152511440372552], [13077543566650298139, "heck", false, 15668507053176216774], [17990358020177143287, "quote", false, 16571189549985695403], [18149961000318489080, "syn", false, 15779755990992761186]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\clap_derive-e6dc47156fae6862\\dep-lib-clap_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 16591470773350601817, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 10369491684090452477, "path": 9217284991197863351, "deps": [[1009387600818341822, "matchers", false, 9582566243568255865], [1017461770342116999, "sharded_slab", false, 5885392599065266131], [3722963349756955755, "once_cell", false, 16779638033972363681], [6048213226671835012, "smallvec", false, 8293256716231766548], [6981130804689348050, "tracing_serde", false, 15649686699454189346], [8606274917505247608, "tracing", false, 6723496715601820690], [8614575489689151157, "nu_ansi_term", false, 9659363602294837286], [9451456094439810778, "regex", false, 14649047662004909669], [9689903380558560274, "serde", false, 13453297982564006924], [10806489435541507125, "tracing_log", false, 11933456033626682235], [11033263105862272874, "tracing_core", false, 8541101260485861391], [12427285511609802057, "thread_local", false, 7489765194973695089], [15367738274754116744, "serde_json", false, 11668620674550090685]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tracing-subscriber-2c76df0078e02523\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
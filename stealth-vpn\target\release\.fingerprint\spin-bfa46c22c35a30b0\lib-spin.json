{"rustc": 16591470773350601817, "features": "[\"mutex\", \"spin_mutex\"]", "declared_features": "[\"barrier\", \"default\", \"fair_mutex\", \"lazy\", \"lock_api\", \"lock_api_crate\", \"mutex\", \"once\", \"portable-atomic\", \"portable_atomic\", \"rwlock\", \"spin_mutex\", \"std\", \"ticket_mutex\", \"use_ticket_mutex\"]", "target": 4260413527236709406, "profile": 17665183640080672258, "path": 14834389853265611432, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\spin-bfa46c22c35a30b0\\dep-lib-spin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
@echo off
setlocal enabledelayedexpansion

REM UltraSecure VPN Build Script for Windows
REM Builds the entire VPN system

echo 🚀 Building UltraSecure VPN - Beyond Military Grade Security
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

REM Add Rust to PATH
set PATH=%PATH%;%USERPROFILE%\.cargo\bin

REM Check if Rust is installed
where cargo >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Rust/Cargo is not installed. Please install Rust from https://rustup.rs/
    pause
    exit /b 1
)

echo [SUCCESS] Rust/Cargo found

REM Check Rust version
for /f "tokens=2" %%i in ('rustc --version') do set RUST_VERSION=%%i
echo [INFO] Rust version: !RUST_VERSION!

REM Create build directory
if not exist build mkdir build

echo [INFO] Building core VPN library...
cd core
cargo build --release
if %errorlevel% neq 0 (
    echo [ERROR] Failed to build core library
    pause
    exit /b 1
)
echo [SUCCESS] Core library built successfully
cd ..

echo [INFO] Building VPN server...
cd server
cargo build --release
if %errorlevel% neq 0 (
    echo [ERROR] Failed to build VPN server
    pause
    exit /b 1
)
echo [SUCCESS] VPN server built successfully
copy target\release\server.exe ..\build\ultrasecure-vpn-server.exe
cd ..

echo [INFO] Building VPN client (CLI)...
cd client
cargo build --release --bin client-cli
if %errorlevel% neq 0 (
    echo [ERROR] Failed to build VPN client
    pause
    exit /b 1
)
echo [SUCCESS] VPN client (CLI) built successfully
copy target\release\client-cli.exe ..\build\ultrasecure-vpn-client.exe
cd ..

echo [INFO] Creating default configuration files...

REM Create server.toml
(
echo [network]
echo vpn_subnet = "10.8.0.0/24"
echo dns_servers = ["1.1.1.1", "8.8.8.8"]
echo mtu = 1420
echo keepalive_interval = 30
echo max_clients = 1000
echo port_range = [51821, 52000]
echo.
echo [security]
echo key_rotation_interval = 3600
echo max_auth_failures = 5
echo ban_duration = 300
echo enable_obfuscation = true
echo obfuscation_mode = "https_like"
echo enable_pfs = true
echo min_license_tier = "basic"
echo validate_hardware_fingerprint = true
echo.
echo [licensing]
echo signing_key = "your-secret-key-here-change-this-in-production"
echo enable_offline_validation = true
echo cache_duration = 3600
echo check_revocation = false
echo grace_period = 86400
echo.
echo [performance]
echo buffer_size = 65536
echo enable_compression = true
echo compression_level = 6
echo enable_bandwidth_limiting = false
echo default_bandwidth_limit = 0
echo enable_connection_pooling = true
echo.
echo [logging]
echo level = "info"
echo log_to_file = true
echo log_file = "vpn_server.log"
echo enable_access_log = true
echo access_log_file = "access.log"
echo rotation_size_mb = 100
echo keep_files = 10
) > build\server.toml

REM Create client.toml
(
echo [connection]
echo auto_reconnect = true
echo reconnect_delay = 5
echo max_reconnect_attempts = 10
echo connection_timeout = 30
echo.
echo [security]
echo verify_server_certificate = true
echo enable_kill_switch = true
echo dns_leak_protection = true
echo ipv6_leak_protection = true
echo.
echo [performance]
echo buffer_size = 65536
echo enable_compression = true
echo mtu = 1420
echo.
echo [logging]
echo level = "info"
echo log_to_file = false
echo log_file = "vpn_client.log"
) > build\client.toml

echo [INFO] Creating startup scripts...

REM Create start-server.bat
(
echo @echo off
echo echo 🚀 Starting UltraSecure VPN Server...
echo ultrasecure-vpn-server.exe --bind 0.0.0.0:51820 --config server.toml
echo pause
) > build\start-server.bat

REM Create generate-license.bat
(
echo @echo off
echo echo 🎫 Generating demo license...
echo ultrasecure-vpn-client.exe generate-license --user-id "demo-user" --tier "premium" --days 30
echo pause
) > build\generate-license.bat

REM Create README.md
(
echo # UltraSecure VPN - Deployment Package
echo.
echo ## Quick Start
echo.
echo ### 1. Start the Server
echo ```
echo start-server.bat
echo ```
echo.
echo ### 2. Generate a License
echo ```
echo generate-license.bat
echo ```
echo.
echo ### 3. Connect a Client
echo ```
echo ultrasecure-vpn-client.exe connect --server ^<SERVER_IP^>:51820 --license "^<LICENSE_TOKEN^>"
echo ```
echo.
echo ## Configuration
echo.
echo - `server.toml` - Server configuration
echo - `client.toml` - Client configuration
echo.
echo ## Security Features
echo.
echo ✅ Triple-layer encryption (XChaCha20-Poly1305 + AES-256-GCM + Custom^)
echo ✅ Advanced traffic obfuscation (HTTPS-like, HTTP/2-like, Steganographic^)
echo ✅ Hardware-bound license authentication
echo ✅ Perfect Forward Secrecy with automatic key rotation
echo ✅ DNS leak protection
echo ✅ Kill switch functionality
echo ✅ Cross-platform support
echo.
echo ## Budget Deployment
echo.
echo This VPN can run on a $5/month VPS with:
echo - 1 CPU core
echo - 1GB RAM
echo - 25GB storage
echo - 1TB bandwidth
echo.
echo Perfect for personal use or small teams!
echo.
echo ## Support
echo.
echo For issues or questions, check the logs:
echo - Server: `vpn_server.log`
echo - Client: `vpn_client.log`
) > build\README.md

echo [SUCCESS] Build completed successfully!
echo.
echo 📦 Build artifacts created in: build\
echo 📋 Files created:
echo    • ultrasecure-vpn-server.exe    - VPN server binary
echo    • ultrasecure-vpn-client.exe    - VPN client binary
echo    • server.toml                   - Server configuration
echo    • client.toml                   - Client configuration
echo    • start-server.bat              - Server startup script
echo    • generate-license.bat          - License generation script
echo    • README.md                     - Deployment instructions
echo.
echo 🚀 Ready to deploy your ultra-secure VPN!
echo 💰 Total cost: ~$5/month for a VPS (cheaper than any commercial VPN!^)
echo.
echo Next steps:
echo 1. Upload the build\ directory to your VPS
echo 2. Run start-server.bat to start the server
echo 3. Generate licenses with generate-license.bat
echo 4. Connect clients using the generated license tokens
echo.
echo 🔒 Security level: BEYOND MILITARY GRADE
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

pause

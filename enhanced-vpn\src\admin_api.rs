//! Admin API for Enhanced VPN Management

use axum::{
    extract::{Query, State},
    http::StatusCode,
    response::Json,
    routing::{get, post, delete},
    Router,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::sync::RwLock;
use tower_http::cors::CorsLayer;

use crate::database::{Database, UsageStats, ConnectionRecord, LicenseRecord};

#[derive(Clone)]
pub struct AppState {
    pub sessions: Arc<RwLock<HashMap<String, crate::ClientSession>>>,
    pub database: Arc<Database>,
    pub stats: Arc<RwLock<crate::ServerStats>>,
}

#[derive(Serialize)]
pub struct ApiResponse<T> {
    success: bool,
    data: Option<T>,
    error: Option<String>,
}

#[derive(Serialize)]
pub struct DashboardData {
    stats: UsageStats,
    recent_connections: Vec<ConnectionRecord>,
    active_sessions: Vec<SessionInfo>,
    system_info: SystemInfo,
}

#[derive(Serialize)]
pub struct SessionInfo {
    session_id: String,
    client_ip: String,
    assigned_ip: String,
    connected_at: u64,
    bytes_sent: u64,
    bytes_received: u64,
    duration_seconds: u64,
}

#[derive(Serialize)]
pub struct SystemInfo {
    uptime_seconds: u64,
    memory_usage_mb: u64,
    cpu_usage_percent: f64,
    network_interfaces: Vec<String>,
}

#[derive(Deserialize)]
pub struct CreateLicenseRequest {
    user_id: String,
    tier: String,
    duration_days: u32,
    hardware_fingerprint: Option<String>,
}

#[derive(Deserialize)]
pub struct BlockIpRequest {
    ip_address: String,
    reason: String,
    duration_hours: Option<u32>,
}

pub async fn start_admin_server(
    bind_addr: SocketAddr,
    sessions: Arc<RwLock<HashMap<String, crate::ClientSession>>>,
    database: Arc<Database>,
    stats: Arc<RwLock<crate::ServerStats>>,
) -> Result<(), Box<dyn std::error::Error>> {
    let state = AppState {
        sessions,
        database,
        stats,
    };

    let app = Router::new()
        .route("/", get(dashboard))
        .route("/api/dashboard", get(get_dashboard_data))
        .route("/api/stats", get(get_stats))
        .route("/api/sessions", get(get_active_sessions))
        .route("/api/sessions/:session_id", delete(disconnect_session))
        .route("/api/connections", get(get_recent_connections))
        .route("/api/licenses", post(create_license))
        .route("/api/licenses/:license_id", delete(revoke_license))
        .route("/api/block-ip", post(block_ip))
        .route("/api/audit-log", get(get_audit_log))
        .route("/api/system-info", get(get_system_info))
        .layer(CorsLayer::permissive())
        .with_state(state);

    let listener = tokio::net::TcpListener::bind(bind_addr).await?;
    tracing::info!("🌐 Admin API server running on http://{}", bind_addr);
    
    axum::serve(listener, app).await?;
    Ok(())
}

async fn dashboard() -> &'static str {
    r#"
<!DOCTYPE html>
<html>
<head>
    <title>UltraSecure VPN Admin</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #fff; }
        .header { background: linear-gradient(45deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .stat-card { background: #2a2a2a; padding: 20px; border-radius: 10px; border-left: 4px solid #667eea; }
        .stat-value { font-size: 2em; font-weight: bold; color: #667eea; }
        .stat-label { color: #ccc; margin-top: 5px; }
        .section { background: #2a2a2a; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .section h3 { margin-top: 0; color: #667eea; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #444; }
        th { background: #333; }
        .btn { background: #667eea; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #5a6fd8; }
        .btn-danger { background: #e74c3c; }
        .btn-danger:hover { background: #c0392b; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 UltraSecure VPN Admin Dashboard</h1>
        <p>🔒 Security Level: QUANTUM-RESISTANT | 🌐 Real-time Monitoring</p>
    </div>
    
    <div class="stats" id="stats">
        <div class="stat-card">
            <div class="stat-value" id="total-connections">-</div>
            <div class="stat-label">Total Connections</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="active-connections">-</div>
            <div class="stat-label">Active Connections</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="data-transferred">-</div>
            <div class="stat-label">Data Transferred</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="unique-users">-</div>
            <div class="stat-label">Unique Users</div>
        </div>
    </div>
    
    <div class="section">
        <h3>📊 Active Sessions</h3>
        <table id="sessions-table">
            <thead>
                <tr>
                    <th>Session ID</th>
                    <th>Client IP</th>
                    <th>Assigned IP</th>
                    <th>Duration</th>
                    <th>Data Transfer</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="sessions-body">
            </tbody>
        </table>
    </div>
    
    <div class="section">
        <h3>🔗 Recent Connections</h3>
        <table id="connections-table">
            <thead>
                <tr>
                    <th>Session ID</th>
                    <th>Client IP</th>
                    <th>Connected At</th>
                    <th>Duration</th>
                    <th>Data Transfer</th>
                </tr>
            </thead>
            <tbody id="connections-body">
            </tbody>
        </table>
    </div>

    <script>
        async function loadDashboard() {
            try {
                const response = await fetch('/api/dashboard');
                const data = await response.json();
                
                if (data.success) {
                    updateStats(data.data.stats);
                    updateSessions(data.data.active_sessions);
                    updateConnections(data.data.recent_connections);
                }
            } catch (error) {
                console.error('Failed to load dashboard:', error);
            }
        }
        
        function updateStats(stats) {
            document.getElementById('total-connections').textContent = stats.total_connections;
            document.getElementById('active-connections').textContent = stats.active_connections;
            document.getElementById('data-transferred').textContent = formatBytes(stats.total_data_transferred);
            document.getElementById('unique-users').textContent = stats.unique_users;
        }
        
        function updateSessions(sessions) {
            const tbody = document.getElementById('sessions-body');
            tbody.innerHTML = '';
            
            sessions.forEach(session => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${session.session_id.substring(0, 8)}...</td>
                    <td>${session.client_ip}</td>
                    <td>${session.assigned_ip}</td>
                    <td>${formatDuration(session.duration_seconds)}</td>
                    <td>${formatBytes(session.bytes_sent + session.bytes_received)}</td>
                    <td><button class="btn btn-danger" onclick="disconnectSession('${session.session_id}')">Disconnect</button></td>
                `;
            });
        }
        
        function updateConnections(connections) {
            const tbody = document.getElementById('connections-body');
            tbody.innerHTML = '';
            
            connections.forEach(conn => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${conn.session_id.substring(0, 8)}...</td>
                    <td>${conn.client_ip}</td>
                    <td>${new Date(conn.connected_at * 1000).toLocaleString()}</td>
                    <td>${formatDuration(conn.duration_seconds)}</td>
                    <td>${formatBytes(conn.bytes_sent + conn.bytes_received)}</td>
                `;
            });
        }
        
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function formatDuration(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            return `${hours}h ${minutes}m ${secs}s`;
        }
        
        async function disconnectSession(sessionId) {
            try {
                const response = await fetch(`/api/sessions/${sessionId}`, { method: 'DELETE' });
                if (response.ok) {
                    loadDashboard(); // Refresh
                }
            } catch (error) {
                console.error('Failed to disconnect session:', error);
            }
        }
        
        // Auto-refresh every 5 seconds
        setInterval(loadDashboard, 5000);
        
        // Initial load
        loadDashboard();
    </script>
</body>
</html>
    "#
}

async fn get_dashboard_data(State(state): State<AppState>) -> Result<Json<ApiResponse<DashboardData>>, StatusCode> {
    match get_dashboard_data_impl(state).await {
        Ok(data) => Ok(Json(ApiResponse {
            success: true,
            data: Some(data),
            error: None,
        })),
        Err(e) => Ok(Json(ApiResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
        })),
    }
}

async fn get_dashboard_data_impl(state: AppState) -> anyhow::Result<DashboardData> {
    let stats = state.database.get_usage_stats().await?;
    let recent_connections = state.database.get_recent_connections(10).await?;
    
    // Get active sessions
    let sessions = state.sessions.read().await;
    let active_sessions: Vec<SessionInfo> = sessions.values().map(|session| {
        let duration = session.created_at.elapsed().as_secs();
        SessionInfo {
            session_id: session.session_id.clone(),
            client_ip: session.client_addr.ip().to_string(),
            assigned_ip: session.assigned_ip.clone(),
            connected_at: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs() - duration,
            bytes_sent: session.bytes_sent,
            bytes_received: session.bytes_received,
            duration_seconds: duration,
        }
    }).collect();
    
    let system_info = SystemInfo {
        uptime_seconds: state.stats.read().await.uptime_start
            .map(|start| start.elapsed().as_secs())
            .unwrap_or(0),
        memory_usage_mb: 0, // Would implement actual memory monitoring
        cpu_usage_percent: 0.0, // Would implement actual CPU monitoring
        network_interfaces: vec!["eth0".to_string()], // Would implement actual interface detection
    };
    
    Ok(DashboardData {
        stats,
        recent_connections,
        active_sessions,
        system_info,
    })
}

async fn get_stats(State(state): State<AppState>) -> Result<Json<ApiResponse<UsageStats>>, StatusCode> {
    match state.database.get_usage_stats().await {
        Ok(stats) => Ok(Json(ApiResponse {
            success: true,
            data: Some(stats),
            error: None,
        })),
        Err(e) => Ok(Json(ApiResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
        })),
    }
}

async fn get_active_sessions(State(state): State<AppState>) -> Result<Json<ApiResponse<Vec<SessionInfo>>>, StatusCode> {
    let sessions = state.sessions.read().await;
    let active_sessions: Vec<SessionInfo> = sessions.values().map(|session| {
        let duration = session.created_at.elapsed().as_secs();
        SessionInfo {
            session_id: session.session_id.clone(),
            client_ip: session.client_addr.ip().to_string(),
            assigned_ip: session.assigned_ip.clone(),
            connected_at: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs() - duration,
            bytes_sent: session.bytes_sent,
            bytes_received: session.bytes_received,
            duration_seconds: duration,
        }
    }).collect();
    
    Ok(Json(ApiResponse {
        success: true,
        data: Some(active_sessions),
        error: None,
    }))
}

async fn disconnect_session(
    axum::extract::Path(session_id): axum::extract::Path<String>,
    State(state): State<AppState>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    let mut sessions = state.sessions.write().await;
    if sessions.remove(&session_id).is_some() {
        let _ = state.database.close_connection(&session_id).await;
        Ok(Json(ApiResponse {
            success: true,
            data: Some(()),
            error: None,
        }))
    } else {
        Ok(Json(ApiResponse {
            success: false,
            data: None,
            error: Some("Session not found".to_string()),
        }))
    }
}

async fn get_recent_connections(State(state): State<AppState>) -> Result<Json<ApiResponse<Vec<ConnectionRecord>>>, StatusCode> {
    match state.database.get_recent_connections(50).await {
        Ok(connections) => Ok(Json(ApiResponse {
            success: true,
            data: Some(connections),
            error: None,
        })),
        Err(e) => Ok(Json(ApiResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
        })),
    }
}

async fn create_license(
    State(state): State<AppState>,
    Json(request): Json<CreateLicenseRequest>,
) -> Result<Json<ApiResponse<String>>, StatusCode> {
    let license_id = uuid::Uuid::new_v4().to_string();
    let now = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs();
    let expires_at = now + (request.duration_days as u64 * 24 * 60 * 60);
    
    let (max_connections, bandwidth_limit, data_limit) = match request.tier.as_str() {
        "basic" => (1, 10_000_000, 10_000_000_000),
        "premium" => (3, 100_000_000, 100_000_000_000),
        "enterprise" => (10, 0, 0),
        _ => (1, 10_000_000, 10_000_000_000),
    };
    
    let license = LicenseRecord {
        license_id: license_id.clone(),
        user_id: request.user_id,
        hardware_fingerprint: request.hardware_fingerprint.unwrap_or_default(),
        tier: request.tier,
        expires_at,
        max_connections,
        bandwidth_limit,
        data_limit,
        is_active: true,
        created_at: now,
    };
    
    match state.database.add_license(license).await {
        Ok(_) => Ok(Json(ApiResponse {
            success: true,
            data: Some(license_id),
            error: None,
        })),
        Err(e) => Ok(Json(ApiResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
        })),
    }
}

async fn revoke_license(
    axum::extract::Path(license_id): axum::extract::Path<String>,
    State(state): State<AppState>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    match state.database.revoke_license(&license_id).await {
        Ok(_) => Ok(Json(ApiResponse {
            success: true,
            data: Some(()),
            error: None,
        })),
        Err(e) => Ok(Json(ApiResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
        })),
    }
}

async fn block_ip(
    State(state): State<AppState>,
    Json(request): Json<BlockIpRequest>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    match state.database.block_ip(&request.ip_address, &request.reason, request.duration_hours).await {
        Ok(_) => Ok(Json(ApiResponse {
            success: true,
            data: Some(()),
            error: None,
        })),
        Err(e) => Ok(Json(ApiResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
        })),
    }
}

async fn get_audit_log(State(state): State<AppState>) -> Result<Json<ApiResponse<Vec<serde_json::Value>>>, StatusCode> {
    match state.database.get_audit_log(100).await {
        Ok(log) => Ok(Json(ApiResponse {
            success: true,
            data: Some(log),
            error: None,
        })),
        Err(e) => Ok(Json(ApiResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
        })),
    }
}

async fn get_system_info(State(state): State<AppState>) -> Result<Json<ApiResponse<SystemInfo>>, StatusCode> {
    let system_info = SystemInfo {
        uptime_seconds: state.stats.read().await.uptime_start
            .map(|start| start.elapsed().as_secs())
            .unwrap_or(0),
        memory_usage_mb: 0, // Would implement actual monitoring
        cpu_usage_percent: 0.0, // Would implement actual monitoring
        network_interfaces: vec!["eth0".to_string()], // Would implement actual detection
    };
    
    Ok(Json(ApiResponse {
        success: true,
        data: Some(system_info),
        error: None,
    }))
}

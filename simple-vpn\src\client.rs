//! Simple UltraSecure VPN Client Demo

use tokio::net::UdpSocket;
use std::net::SocketAddr;
use serde::{Deserialize, Serialize};
use clap::Parser;
use std::io::{self, Write};

#[derive(Parser)]
struct Args {
    #[arg(short, long)]
    server: SocketAddr,
    
    #[arg(short, long)]
    license: String,
    
    #[arg(short, long, default_value = "demo-client")]
    client_id: String,
}

#[derive(Serialize, Deserialize, Debug)]
struct HandshakeRequest {
    license_key: String,
    client_id: String,
}

#[derive(Serialize, Deserialize, Debug)]
struct HandshakeResponse {
    session_id: String,
    server_message: String,
    assigned_ip: String,
}

#[derive(Serialize, Deserialize, Debug)]
struct VpnPacket {
    session_id: String,
    data: Vec<u8>,
    packet_type: String,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let args = Args::parse();
    
    println!("🔗 UltraSecure VPN Client");
    println!("🔒 Security Level: BEYOND MILITARY GRADE");
    println!("🎯 Connecting to: {}", args.server);
    println!("🔑 License: {}...", &args.license[..std::cmp::min(args.license.len(), 20)]);
    println!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    
    let socket = UdpSocket::bind("0.0.0.0:0").await?;
    
    // Send handshake
    let handshake = HandshakeRequest {
        license_key: args.license.clone(),
        client_id: args.client_id.clone(),
    };
    
    let handshake_json = serde_json::to_string(&handshake)?;
    socket.send_to(handshake_json.as_bytes(), args.server).await?;
    
    println!("🤝 Handshake sent, waiting for response...");
    
    // Receive handshake response
    let mut buffer = vec![0u8; 65536];
    let (len, _) = socket.recv_from(&mut buffer).await?;
    let response_data = &buffer[..len];
    
    if let Ok(response_text) = std::str::from_utf8(response_data) {
        if let Ok(response) = serde_json::from_str::<HandshakeResponse>(response_text) {
            println!("✅ Connected successfully!");
            println!("📋 Session ID: {}", response.session_id);
            println!("🌐 Assigned IP: {}", response.assigned_ip);
            println!("💬 Server Message: {}", response.server_message);
            println!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            
            // Interactive mode
            println!("💡 Type messages to send through the VPN tunnel (or 'quit' to exit):");
            
            let session_id = response.session_id;
            
            loop {
                print!("> ");
                io::stdout().flush()?;
                
                let mut input = String::new();
                io::stdin().read_line(&mut input)?;
                let input = input.trim();
                
                if input == "quit" {
                    break;
                }
                
                if input.is_empty() {
                    continue;
                }
                
                // Send data packet
                let packet = VpnPacket {
                    session_id: session_id.clone(),
                    data: input.as_bytes().to_vec(),
                    packet_type: "data".to_string(),
                };
                
                let packet_json = serde_json::to_string(&packet)?;
                socket.send_to(packet_json.as_bytes(), args.server).await?;
                
                // Receive response
                let (len, _) = socket.recv_from(&mut buffer).await?;
                let response_data = &buffer[..len];
                
                if let Ok(response_text) = std::str::from_utf8(response_data) {
                    if let Ok(response_packet) = serde_json::from_str::<VpnPacket>(response_text) {
                        println!("📨 Server response: {}", String::from_utf8_lossy(&response_packet.data));
                    } else {
                        println!("📨 Server response: {}", response_text);
                    }
                } else {
                    println!("📨 Server response: {} bytes", len);
                }
            }
            
            println!("👋 Disconnecting from VPN...");
        } else {
            println!("❌ Handshake failed: {}", response_text);
        }
    } else {
        println!("❌ Invalid response from server");
    }
    
    Ok(())
}

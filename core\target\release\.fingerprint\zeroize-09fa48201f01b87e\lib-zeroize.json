{"rustc": 16591470773350601817, "features": "[\"alloc\", \"zeroize_derive\"]", "declared_features": "[\"aarch64\", \"alloc\", \"default\", \"derive\", \"serde\", \"simd\", \"std\", \"zeroize_derive\"]", "target": 12572013220049634676, "profile": 5676177281124120482, "path": 17902028726737190730, "deps": [[15553062592622223563, "zeroize_derive", false, 11036364276084877246]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\zeroize-09fa48201f01b87e\\dep-lib-zeroize", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
#!/bin/bash

# UltraSecure VPN Build Script
# Builds the entire VPN system for all platforms

set -e

echo "🚀 Building UltraSecure VPN - Beyond Military Grade Security"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Rust is installed
if ! command -v cargo &> /dev/null; then
    print_error "Rust/Cargo is not installed. Please install Rust from https://rustup.rs/"
    exit 1
fi

print_success "Rust/Cargo found"

# Check Rust version
RUST_VERSION=$(rustc --version | cut -d' ' -f2)
print_status "Rust version: $RUST_VERSION"

# Create build directory
BUILD_DIR="build"
mkdir -p $BUILD_DIR

print_status "Building core VPN library..."
cd core
if cargo build --release; then
    print_success "Core library built successfully"
else
    print_error "Failed to build core library"
    exit 1
fi
cd ..

print_status "Building VPN server..."
cd server
if cargo build --release; then
    print_success "VPN server built successfully"
    cp target/release/server ../$BUILD_DIR/ultrasecure-vpn-server
else
    print_error "Failed to build VPN server"
    exit 1
fi
cd ..

print_status "Building VPN client (CLI)..."
cd client
if cargo build --release --bin client-cli; then
    print_success "VPN client (CLI) built successfully"
    cp target/release/client-cli ../$BUILD_DIR/ultrasecure-vpn-client
else
    print_error "Failed to build VPN client"
    exit 1
fi
cd ..

# Create configuration files
print_status "Creating default configuration files..."

cat > $BUILD_DIR/server.toml << 'EOF'
[network]
vpn_subnet = "10.8.0.0/24"
dns_servers = ["1.1.1.1", "8.8.8.8"]
mtu = 1420
keepalive_interval = 30
max_clients = 1000
port_range = [51821, 52000]

[security]
key_rotation_interval = 3600
max_auth_failures = 5
ban_duration = 300
enable_obfuscation = true
obfuscation_mode = "https_like"
enable_pfs = true
min_license_tier = "basic"
validate_hardware_fingerprint = true

[licensing]
signing_key = "your-secret-key-here-change-this-in-production"
enable_offline_validation = true
cache_duration = 3600
check_revocation = false
grace_period = 86400

[performance]
buffer_size = 65536
enable_compression = true
compression_level = 6
enable_bandwidth_limiting = false
default_bandwidth_limit = 0
enable_connection_pooling = true

[logging]
level = "info"
log_to_file = true
log_file = "vpn_server.log"
enable_access_log = true
access_log_file = "access.log"
rotation_size_mb = 100
keep_files = 10
EOF

cat > $BUILD_DIR/client.toml << 'EOF'
[connection]
auto_reconnect = true
reconnect_delay = 5
max_reconnect_attempts = 10
connection_timeout = 30

[security]
verify_server_certificate = true
enable_kill_switch = true
dns_leak_protection = true
ipv6_leak_protection = true

[performance]
buffer_size = 65536
enable_compression = true
mtu = 1420

[logging]
level = "info"
log_to_file = false
log_file = "vpn_client.log"
EOF

# Create startup scripts
print_status "Creating startup scripts..."

cat > $BUILD_DIR/start-server.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting UltraSecure VPN Server..."
./ultrasecure-vpn-server --bind 0.0.0.0:51820 --config server.toml
EOF

cat > $BUILD_DIR/start-server.bat << 'EOF'
@echo off
echo 🚀 Starting UltraSecure VPN Server...
ultrasecure-vpn-server.exe --bind 0.0.0.0:51820 --config server.toml
pause
EOF

cat > $BUILD_DIR/generate-license.sh << 'EOF'
#!/bin/bash
echo "🎫 Generating demo license..."
./ultrasecure-vpn-client generate-license --user-id "demo-user" --tier "premium" --days 30
EOF

cat > $BUILD_DIR/generate-license.bat << 'EOF'
@echo off
echo 🎫 Generating demo license...
ultrasecure-vpn-client.exe generate-license --user-id "demo-user" --tier "premium" --days 30
pause
EOF

chmod +x $BUILD_DIR/*.sh

# Create README for deployment
cat > $BUILD_DIR/README.md << 'EOF'
# UltraSecure VPN - Deployment Package

## Quick Start

### 1. Start the Server
```bash
# Linux/macOS
./start-server.sh

# Windows
start-server.bat
```

### 2. Generate a License
```bash
# Linux/macOS
./generate-license.sh

# Windows
generate-license.bat
```

### 3. Connect a Client
```bash
# Linux/macOS
./ultrasecure-vpn-client connect --server <SERVER_IP>:51820 --license "<LICENSE_TOKEN>"

# Windows
ultrasecure-vpn-client.exe connect --server <SERVER_IP>:51820 --license "<LICENSE_TOKEN>"
```

## Configuration

- `server.toml` - Server configuration
- `client.toml` - Client configuration

## Security Features

✅ Triple-layer encryption (XChaCha20-Poly1305 + AES-256-GCM + Custom)
✅ Advanced traffic obfuscation (HTTPS-like, HTTP/2-like, Steganographic)
✅ Hardware-bound license authentication
✅ Perfect Forward Secrecy with automatic key rotation
✅ DNS leak protection
✅ Kill switch functionality
✅ Cross-platform support

## Budget Deployment

This VPN can run on a $5/month VPS with:
- 1 CPU core
- 1GB RAM
- 25GB storage
- 1TB bandwidth

Perfect for personal use or small teams!

## Support

For issues or questions, check the logs:
- Server: `vpn_server.log`
- Client: `vpn_client.log`
EOF

print_success "Build completed successfully!"
echo ""
echo "📦 Build artifacts created in: $BUILD_DIR/"
echo "📋 Files created:"
echo "   • ultrasecure-vpn-server    - VPN server binary"
echo "   • ultrasecure-vpn-client    - VPN client binary"
echo "   • server.toml               - Server configuration"
echo "   • client.toml               - Client configuration"
echo "   • start-server.sh/.bat      - Server startup scripts"
echo "   • generate-license.sh/.bat  - License generation scripts"
echo "   • README.md                 - Deployment instructions"
echo ""
echo "🚀 Ready to deploy your ultra-secure VPN!"
echo "💰 Total cost: ~$5/month for a VPS (cheaper than any commercial VPN!)"
echo ""
echo "Next steps:"
echo "1. Upload the build/ directory to your VPS"
echo "2. Run ./start-server.sh to start the server"
echo "3. Generate licenses with ./generate-license.sh"
echo "4. Connect clients using the generated license tokens"
echo ""
echo "🔒 Security level: BEYOND MILITARY GRADE"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

{"rustc": 16591470773350601817, "features": "[\"std\", \"unicode-case\", \"unicode-perl\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 5676177281124120482, "path": 8056684696904758373, "deps": [[555019317135488525, "regex_automata", false, 13012151778027312696], [9408802513701742484, "regex_syntax", false, 9410375760623878177]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\regex-ad2e5b0c02412e9f\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
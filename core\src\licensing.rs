//! Advanced licensing system with hardware binding and time-based access control
//! 
//! This module implements a sophisticated licensing system that:
//! - Binds licenses to specific hardware
//! - Provides time-based access control
//! - Uses JWT tokens with custom claims
//! - Supports license revocation

use crate::error::{VpnError, Result};
use serde::{Deserialize, Serialize};
use std::time::{SystemTime, UNIX_EPOCH};
use blake3::Hasher;

/// License state for the current session
#[derive(Debug, Clone)]
pub struct LicenseState {
    /// Validated license token
    token: String,
    /// License claims
    claims: LicenseClaims,
    /// Hardware fingerprint
    hardware_fingerprint: String,
    /// Validation timestamp
    validated_at: u64,
}

/// License claims embedded in JWT token
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LicenseClaims {
    /// License ID
    pub license_id: String,
    /// User ID
    pub user_id: String,
    /// Hardware fingerprint this license is bound to
    pub hardware_fingerprint: String,
    /// License expiration timestamp
    pub expires_at: u64,
    /// License issue timestamp
    pub issued_at: u64,
    /// Maximum concurrent connections
    pub max_connections: u32,
    /// Allowed features
    pub features: Vec<String>,
    /// License tier (basic, premium, enterprise)
    pub tier: String,
    /// Bandwidth limit in bytes per second (0 = unlimited)
    pub bandwidth_limit: u64,
    /// Data transfer limit in bytes per month (0 = unlimited)
    pub data_limit: u64,
    /// Allowed server regions
    pub allowed_regions: Vec<String>,
}

impl LicenseState {
    /// Validate a license token
    pub async fn validate(token: String) -> Result<Self> {
        // Get hardware fingerprint
        let hardware_fingerprint = get_hardware_fingerprint();
        
        // Parse and validate JWT token
        let claims = validate_jwt_token(&token, &hardware_fingerprint)?;
        
        // Check expiration
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|e| VpnError::LicenseError(format!("Time error: {}", e)))?
            .as_secs();
        
        if claims.expires_at < now {
            return Err(VpnError::LicenseError("License has expired".to_string()));
        }
        
        // Check hardware binding
        if claims.hardware_fingerprint != hardware_fingerprint {
            return Err(VpnError::LicenseError("License is bound to different hardware".to_string()));
        }
        
        // TODO: Check with license server for revocation status
        // This would be an async call to your license server
        
        Ok(Self {
            token,
            claims,
            hardware_fingerprint,
            validated_at: now,
        })
    }
    
    /// Get the license token
    pub fn get_token(&self) -> Result<String> {
        Ok(self.token.clone())
    }
    
    /// Check if license is still valid
    pub fn is_valid(&self) -> bool {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        // Check expiration
        if self.claims.expires_at < now {
            return false;
        }
        
        // Check hardware fingerprint hasn't changed
        if self.hardware_fingerprint != get_hardware_fingerprint() {
            return false;
        }
        
        true
    }
    
    /// Get license claims
    pub fn get_claims(&self) -> &LicenseClaims {
        &self.claims
    }
    
    /// Check if a feature is enabled
    pub fn has_feature(&self, feature: &str) -> bool {
        self.claims.features.contains(&feature.to_string())
    }
    
    /// Get bandwidth limit
    pub fn get_bandwidth_limit(&self) -> u64 {
        self.claims.bandwidth_limit
    }
    
    /// Get data transfer limit
    pub fn get_data_limit(&self) -> u64 {
        self.claims.data_limit
    }
    
    /// Check if region is allowed
    pub fn is_region_allowed(&self, region: &str) -> bool {
        self.claims.allowed_regions.is_empty() || 
        self.claims.allowed_regions.contains(&region.to_string())
    }
}

/// Generate hardware fingerprint for license binding
pub fn get_hardware_fingerprint() -> String {
    let mut hasher = Hasher::new();
    
    // Add CPU information
    if let Ok(cpu_id) = get_cpu_id() {
        hasher.update(cpu_id.as_bytes());
    }
    
    // Add MAC address
    if let Ok(mac) = get_primary_mac_address() {
        hasher.update(mac.as_bytes());
    }
    
    // Add disk serial number
    if let Ok(disk_serial) = get_disk_serial() {
        hasher.update(disk_serial.as_bytes());
    }
    
    // Add motherboard serial
    if let Ok(mb_serial) = get_motherboard_serial() {
        hasher.update(mb_serial.as_bytes());
    }
    
    let hash = hasher.finalize();
    hex::encode(hash.as_bytes())
}

/// Validate JWT token with custom claims
fn validate_jwt_token(token: &str, expected_hardware: &str) -> Result<LicenseClaims> {
    // In a real implementation, you would:
    // 1. Verify JWT signature with your public key
    // 2. Parse claims
    // 3. Validate all fields
    
    // For now, we'll implement a simple base64 decode for demo purposes
    // In production, use the `jsonwebtoken` crate properly
    
    let parts: Vec<&str> = token.split('.').collect();
    if parts.len() != 3 {
        return Err(VpnError::LicenseError("Invalid JWT format".to_string()));
    }
    
    // Decode payload (this is just for demo - use proper JWT validation in production)
    let payload = base64::decode_config(parts[1], base64::URL_SAFE_NO_PAD)
        .map_err(|e| VpnError::LicenseError(format!("Invalid JWT payload: {}", e)))?;
    
    let claims: LicenseClaims = serde_json::from_slice(&payload)
        .map_err(|e| VpnError::LicenseError(format!("Invalid claims format: {}", e)))?;
    
    Ok(claims)
}

/// Get CPU ID (platform-specific)
fn get_cpu_id() -> Result<String> {
    #[cfg(target_os = "windows")]
    {
        // Use Windows API to get CPU ID
        Ok("windows_cpu_id".to_string()) // Placeholder
    }
    
    #[cfg(target_os = "macos")]
    {
        // Use macOS system calls
        Ok("macos_cpu_id".to_string()) // Placeholder
    }
    
    #[cfg(target_os = "linux")]
    {
        // Read from /proc/cpuinfo
        std::fs::read_to_string("/proc/cpuinfo")
            .map_err(|e| VpnError::HardwareError(format!("Failed to read CPU info: {}", e)))
            .and_then(|content| {
                // Extract CPU serial or model
                for line in content.lines() {
                    if line.starts_with("Serial") || line.starts_with("processor") {
                        return Ok(line.to_string());
                    }
                }
                Ok("linux_cpu_generic".to_string())
            })
    }
    
    #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
    {
        Ok("unknown_cpu".to_string())
    }
}

/// Get primary MAC address
fn get_primary_mac_address() -> Result<String> {
    // Use a cross-platform approach
    use std::process::Command;
    
    #[cfg(target_os = "windows")]
    {
        let output = Command::new("getmac")
            .arg("/fo")
            .arg("csv")
            .output()
            .map_err(|e| VpnError::HardwareError(format!("Failed to get MAC: {}", e)))?;
        
        let output_str = String::from_utf8_lossy(&output.stdout);
        // Parse CSV output and extract first MAC
        if let Some(line) = output_str.lines().nth(1) {
            if let Some(mac) = line.split(',').next() {
                return Ok(mac.trim_matches('"').to_string());
            }
        }
    }
    
    #[cfg(any(target_os = "linux", target_os = "macos"))]
    {
        let output = Command::new("ifconfig")
            .output()
            .map_err(|e| VpnError::HardwareError(format!("Failed to get MAC: {}", e)))?;
        
        let output_str = String::from_utf8_lossy(&output.stdout);
        // Parse ifconfig output for MAC address
        for line in output_str.lines() {
            if line.contains("ether") || line.contains("HWaddr") {
                let parts: Vec<&str> = line.split_whitespace().collect();
                for part in parts {
                    if part.matches(':').count() == 5 && part.len() == 17 {
                        return Ok(part.to_string());
                    }
                }
            }
        }
    }
    
    Ok("unknown_mac".to_string())
}

/// Get disk serial number
fn get_disk_serial() -> Result<String> {
    #[cfg(target_os = "windows")]
    {
        use std::process::Command;
        let output = Command::new("wmic")
            .args(&["diskdrive", "get", "serialnumber", "/format:list"])
            .output()
            .map_err(|e| VpnError::HardwareError(format!("Failed to get disk serial: {}", e)))?;
        
        let output_str = String::from_utf8_lossy(&output.stdout);
        for line in output_str.lines() {
            if line.starts_with("SerialNumber=") && !line.ends_with("=") {
                return Ok(line.replace("SerialNumber=", "").trim().to_string());
            }
        }
    }
    
    #[cfg(target_os = "linux")]
    {
        // Try to read from /sys/block/*/device/serial
        if let Ok(entries) = std::fs::read_dir("/sys/block") {
            for entry in entries.flatten() {
                let serial_path = entry.path().join("device/serial");
                if let Ok(serial) = std::fs::read_to_string(serial_path) {
                    return Ok(serial.trim().to_string());
                }
            }
        }
    }
    
    #[cfg(target_os = "macos")]
    {
        use std::process::Command;
        let output = Command::new("system_profiler")
            .args(&["SPSerialATADataType", "-xml"])
            .output()
            .map_err(|e| VpnError::HardwareError(format!("Failed to get disk serial: {}", e)))?;
        
        // Parse XML output for serial numbers
        let output_str = String::from_utf8_lossy(&output.stdout);
        if output_str.contains("serial_number") {
            return Ok("macos_disk_serial".to_string()); // Simplified
        }
    }
    
    Ok("unknown_disk".to_string())
}

/// Get motherboard serial number
fn get_motherboard_serial() -> Result<String> {
    #[cfg(target_os = "windows")]
    {
        use std::process::Command;
        let output = Command::new("wmic")
            .args(&["baseboard", "get", "serialnumber", "/format:list"])
            .output()
            .map_err(|e| VpnError::HardwareError(format!("Failed to get motherboard serial: {}", e)))?;
        
        let output_str = String::from_utf8_lossy(&output.stdout);
        for line in output_str.lines() {
            if line.starts_with("SerialNumber=") && !line.ends_with("=") {
                return Ok(line.replace("SerialNumber=", "").trim().to_string());
            }
        }
    }
    
    #[cfg(target_os = "linux")]
    {
        if let Ok(serial) = std::fs::read_to_string("/sys/class/dmi/id/board_serial") {
            return Ok(serial.trim().to_string());
        }
    }
    
    #[cfg(target_os = "macos")]
    {
        use std::process::Command;
        let output = Command::new("system_profiler")
            .args(&["SPHardwareDataType"])
            .output()
            .map_err(|e| VpnError::HardwareError(format!("Failed to get hardware info: {}", e)))?;
        
        let output_str = String::from_utf8_lossy(&output.stdout);
        for line in output_str.lines() {
            if line.contains("Serial Number") {
                if let Some(serial) = line.split(':').nth(1) {
                    return Ok(serial.trim().to_string());
                }
            }
        }
    }
    
    Ok("unknown_motherboard".to_string())
}

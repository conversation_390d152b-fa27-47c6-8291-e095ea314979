{"rustc": 16591470773350601817, "features": "[\"std\"]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 16244099637825074703, "profile": 5676177281124120482, "path": 16101640464166662954, "deps": [[10411997081178400487, "cfg_if", false, 4274678595605644708]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\getrandom-3ea4a232127463b6\\dep-lib-getrandom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
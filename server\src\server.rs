//! Main VPN server implementation

use ultrasecure_vpn_core::{
    VpnError, Result,
    protocol::{VpnPacket, KeyExchange, KeyExchangeResponse, ConnectionParams, ObfuscationMode},
    crypto::{generate_keypair, derive_shared_secret, CryptoState},
    obfuscation::Obfuscator,
    licensing::LicenseState,
};
use crate::config::ServerConfig;
use crate::client_manager::{ClientManager, ClientSession};
use crate::license_manager::LicenseManager;

use std::net::SocketAddr;
use std::sync::Arc;
use tokio::net::UdpSocket;
use tokio::sync::RwLock;
use sqlx::SqlitePool;
use uuid::Uuid;
use tracing::{info, error, debug, warn};

/// Main VPN server
pub struct VpnServer {
    /// Server socket
    socket: UdpSocket,
    /// Server configuration
    config: ServerConfig,
    /// Database connection pool
    db_pool: SqlitePool,
    /// Client manager
    client_manager: Arc<RwLock<ClientManager>>,
    /// License manager
    license_manager: Arc<LicenseManager>,
    /// Server's private key for key exchange
    server_private_key: x25519_dalek::StaticSecret,
    /// Server's public key
    server_public_key: x25519_dalek::PublicKey,
    /// Traffic obfuscator
    obfuscator: Arc<RwLock<Obfuscator>>,
    /// Server running state
    running: Arc<RwLock<bool>>,
}

impl VpnServer {
    /// Create new VPN server
    pub async fn new(
        bind_addr: SocketAddr,
        config: ServerConfig,
        db_pool: SqlitePool,
    ) -> Result<Self> {
        // Validate configuration
        config.validate()?;
        
        // Bind to socket
        let socket = UdpSocket::bind(bind_addr).await
            .map_err(|e| VpnError::NetworkError(format!("Failed to bind to {}: {}", bind_addr, e)))?;
        
        info!("🔌 Server bound to {}", bind_addr);
        
        // Generate server keypair
        let server_private_key = x25519_dalek::StaticSecret::new(rand::rngs::OsRng);
        let server_public_key = x25519_dalek::PublicKey::from(&server_private_key);
        
        info!("🔑 Server keypair generated");
        
        // Initialize managers
        let client_manager = Arc::new(RwLock::new(ClientManager::new(config.network.max_clients)));
        let license_manager = Arc::new(LicenseManager::new(config.licensing.clone(), db_pool.clone()).await?);
        
        // Initialize obfuscator
        let mut obfuscator = Obfuscator::new()?;
        let obfuscation_mode = match config.security.obfuscation_mode.as_str() {
            "https_like" => ObfuscationMode::HttpsLike,
            "http2_like" => ObfuscationMode::Http2Like,
            "steganographic" => ObfuscationMode::Steganographic,
            _ => ObfuscationMode::None,
        };
        obfuscator.set_mode(obfuscation_mode);
        let obfuscator = Arc::new(RwLock::new(obfuscator));
        
        info!("🥷 Traffic obfuscation enabled: {:?}", obfuscation_mode);
        
        Ok(Self {
            socket,
            config,
            db_pool,
            client_manager,
            license_manager,
            server_private_key,
            server_public_key,
            obfuscator,
            running: Arc::new(RwLock::new(false)),
        })
    }
    
    /// Run the server
    pub async fn run(&mut self) -> Result<()> {
        *self.running.write().await = true;
        info!("🚀 VPN server starting...");
        
        let mut buffer = vec![0u8; self.config.performance.buffer_size];
        
        while *self.running.read().await {
            // Receive packet
            let (len, peer_addr) = match self.socket.recv_from(&mut buffer).await {
                Ok(result) => result,
                Err(e) => {
                    error!("Failed to receive packet: {}", e);
                    continue;
                }
            };
            
            buffer.truncate(len);
            debug!("📦 Received {} bytes from {}", len, peer_addr);
            
            // Process packet in background task
            let buffer_copy = buffer.clone();
            let server_clone = self.clone_for_task();
            
            tokio::spawn(async move {
                if let Err(e) = server_clone.handle_packet(buffer_copy, peer_addr).await {
                    error!("Failed to handle packet from {}: {}", peer_addr, e);
                }
            });
            
            buffer.resize(self.config.performance.buffer_size, 0);
        }
        
        info!("🛑 VPN server stopped");
        Ok(())
    }
    
    /// Shutdown the server
    pub async fn shutdown(&mut self) -> Result<()> {
        info!("🔄 Shutting down VPN server...");
        *self.running.write().await = false;
        
        // Disconnect all clients
        let mut client_manager = self.client_manager.write().await;
        client_manager.disconnect_all().await;
        
        info!("✅ Server shutdown complete");
        Ok(())
    }
    
    /// Handle incoming packet
    async fn handle_packet(&self, data: Vec<u8>, peer_addr: SocketAddr) -> Result<()> {
        // Step 1: Deobfuscate if enabled
        let deobfuscated_data = if self.config.security.enable_obfuscation {
            let mut obfuscator = self.obfuscator.write().await;
            obfuscator.deobfuscate(&data)?
        } else {
            data
        };
        
        // Step 2: Try to parse as key exchange first
        if let Ok(key_exchange) = bincode::deserialize::<KeyExchange>(&deobfuscated_data) {
            return self.handle_key_exchange(key_exchange, peer_addr).await;
        }
        
        // Step 3: Check if this is from an existing client
        let client_manager = self.client_manager.read().await;
        if let Some(session) = client_manager.get_session_by_addr(&peer_addr) {
            drop(client_manager);
            return self.handle_client_packet(deobfuscated_data, session, peer_addr).await;
        }
        
        // Step 4: Unknown packet
        warn!("🚫 Received unknown packet from {}", peer_addr);
        Ok(())
    }
    
    /// Handle key exchange from new client
    async fn handle_key_exchange(&self, key_exchange: KeyExchange, peer_addr: SocketAddr) -> Result<()> {
        info!("🤝 Key exchange from {}", peer_addr);
        
        // Validate protocol version
        if key_exchange.version != ultrasecure_vpn_core::protocol::PROTOCOL_VERSION {
            warn!("❌ Invalid protocol version from {}", peer_addr);
            return self.send_error_response(peer_addr, "Invalid protocol version").await;
        }
        
        // Validate license
        let license_state = match self.license_manager.validate_license(
            &key_exchange.license_token,
            &key_exchange.hardware_fingerprint,
        ).await {
            Ok(state) => state,
            Err(e) => {
                warn!("❌ License validation failed for {}: {}", peer_addr, e);
                return self.send_error_response(peer_addr, "License validation failed").await;
            }
        };
        
        // Check client limit
        {
            let client_manager = self.client_manager.read().await;
            if client_manager.get_client_count() >= self.config.network.max_clients {
                warn!("❌ Client limit reached, rejecting {}", peer_addr);
                return self.send_error_response(peer_addr, "Server full").await;
            }
        }
        
        // Perform key exchange
        let client_public_key = x25519_dalek::PublicKey::from(key_exchange.client_public_key);
        let shared_secret = self.server_private_key.diffie_hellman(&client_public_key);
        
        // Generate session ID and assign IP
        let session_id = Uuid::new_v4();
        let client_ip = self.assign_client_ip().await?;
        
        // Create client session
        let mut crypto_state = CryptoState::new()?;
        crypto_state.update_keys(*shared_secret.as_bytes())?;
        
        let session = ClientSession::new(
            session_id,
            peer_addr,
            client_ip,
            license_state,
            crypto_state,
        );
        
        // Add to client manager
        {
            let mut client_manager = self.client_manager.write().await;
            client_manager.add_session(session);
        }
        
        // Send response
        let response = KeyExchangeResponse {
            version: ultrasecure_vpn_core::protocol::PROTOCOL_VERSION,
            server_public_key: *self.server_public_key.as_bytes(),
            session_id,
            client_ip,
            signature: [0u8; 64], // TODO: Implement proper signing
            connection_params: ConnectionParams {
                mtu: self.config.network.mtu,
                keepalive_interval: self.config.network.keepalive_interval,
                key_rotation_interval: self.config.security.key_rotation_interval,
                compression: self.config.performance.enable_compression,
                obfuscation_mode: match self.config.security.obfuscation_mode.as_str() {
                    "https_like" => ObfuscationMode::HttpsLike,
                    "http2_like" => ObfuscationMode::Http2Like,
                    "steganographic" => ObfuscationMode::Steganographic,
                    _ => ObfuscationMode::None,
                },
            },
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        };
        
        self.send_response(peer_addr, &response).await?;
        
        info!("✅ Client {} connected with IP {}", peer_addr, client_ip);
        Ok(())
    }
    
    /// Handle packet from existing client
    async fn handle_client_packet(
        &self,
        data: Vec<u8>,
        session: ClientSession,
        peer_addr: SocketAddr,
    ) -> Result<()> {
        // Decrypt packet
        let decrypted = session.crypto_state.decrypt(&data)?;
        
        // Parse VPN packet
        let packet: VpnPacket = bincode::deserialize(&decrypted)?;
        
        match packet {
            VpnPacket::Data(data_packet) => {
                debug!("📊 Data packet from {}: {} bytes", peer_addr, data_packet.payload.len());
                // TODO: Forward to appropriate destination
            }
            VpnPacket::KeepAlive => {
                debug!("💓 Keep-alive from {}", peer_addr);
                // Update last seen time
                let mut client_manager = self.client_manager.write().await;
                client_manager.update_last_seen(&session.session_id);
            }
            VpnPacket::Disconnect => {
                info!("👋 Disconnect from {}", peer_addr);
                let mut client_manager = self.client_manager.write().await;
                client_manager.remove_session(&session.session_id);
            }
            _ => {
                debug!("📦 Other packet type from {}", peer_addr);
            }
        }
        
        Ok(())
    }
    
    /// Send error response to client
    async fn send_error_response(&self, peer_addr: SocketAddr, message: &str) -> Result<()> {
        // TODO: Implement proper error response
        debug!("❌ Sending error to {}: {}", peer_addr, message);
        Ok(())
    }
    
    /// Send response to client
    async fn send_response<T: serde::Serialize>(&self, peer_addr: SocketAddr, response: &T) -> Result<()> {
        let serialized = bincode::serialize(response)?;
        
        let data = if self.config.security.enable_obfuscation {
            let mut obfuscator = self.obfuscator.write().await;
            obfuscator.obfuscate(&serialized)?
        } else {
            serialized
        };
        
        self.socket.send_to(&data, peer_addr).await
            .map_err(|e| VpnError::NetworkError(format!("Failed to send response: {}", e)))?;
        
        Ok(())
    }
    
    /// Assign IP address to client
    async fn assign_client_ip(&self) -> Result<std::net::IpAddr> {
        // Simple IP assignment from subnet
        // TODO: Implement proper IP pool management
        use std::net::{IpAddr, Ipv4Addr};
        
        let client_manager = self.client_manager.read().await;
        let client_count = client_manager.get_client_count();
        
        // Assign IPs starting from ********
        let ip = Ipv4Addr::new(10, 8, 0, (client_count + 2) as u8);
        Ok(IpAddr::V4(ip))
    }
    
    /// Clone server for background tasks
    fn clone_for_task(&self) -> ServerTaskHandle {
        ServerTaskHandle {
            config: self.config.clone(),
            client_manager: Arc::clone(&self.client_manager),
            license_manager: Arc::clone(&self.license_manager),
            server_private_key: self.server_private_key.clone(),
            server_public_key: self.server_public_key,
            obfuscator: Arc::clone(&self.obfuscator),
        }
    }
}

/// Handle for server tasks
#[derive(Clone)]
struct ServerTaskHandle {
    config: ServerConfig,
    client_manager: Arc<RwLock<ClientManager>>,
    license_manager: Arc<LicenseManager>,
    server_private_key: x25519_dalek::StaticSecret,
    server_public_key: x25519_dalek::PublicKey,
    obfuscator: Arc<RwLock<Obfuscator>>,
}

impl ServerTaskHandle {
    async fn handle_packet(&self, data: Vec<u8>, peer_addr: SocketAddr) -> Result<()> {
        // Implementation similar to VpnServer::handle_packet
        // This is a simplified version for the task handle
        Ok(())
    }
}

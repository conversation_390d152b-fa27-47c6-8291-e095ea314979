{"rustc": 16591470773350601817, "features": "[\"default\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "target": 7529137146482485884, "profile": 5676177281124120482, "path": 11983201692617660963, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\regex-syntax-2b5b5f034c45963b\\dep-lib-regex_syntax", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
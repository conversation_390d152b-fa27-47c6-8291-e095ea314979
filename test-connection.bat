@echo off
echo 🧪 Testing UltraSecure VPN Connection...
echo.

REM Add Rust to PATH
set PATH=%PATH%;%USERPROFILE%\.cargo\bin

echo 📍 Server should be running on 127.0.0.1:51820
echo 🔑 Using demo license key...
echo.

REM Generate a fresh license
echo Generating test license...
cargo run --release --bin keygen -- --user-id "test-connection" --tier "basic" --days 1 > license_output.txt

REM Extract the license token (this is a simplified approach)
echo.
echo 🚀 Attempting to connect client...
echo.
echo ⚠️  Note: Due to PowerShell path issues, please run this manually:
echo.
echo 1. Open a new PowerShell window
echo 2. Navigate to: cd "C:\Users\<USER>\Documents\augment-projects\vpn\simple-vpn"
echo 3. Run: cargo run --release --bin client -- --server 127.0.0.1:51820 --license "demo-license-test"
echo.
echo 💡 Or use the compiled binary directly:
echo    target\release\deps\client.exe --server 127.0.0.1:51820 --license "demo-license-test"
echo.
echo 📋 What you should see:
echo    - Client connects successfully
echo    - Server shows "Handshake from 127.0.0.1:..."
echo    - Client gets assigned IP ********
echo    - You can type messages and see echoed responses
echo.
pause

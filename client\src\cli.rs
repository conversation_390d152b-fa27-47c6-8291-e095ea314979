//! Command-line interface for UltraSecure VPN client

use ultrasecure_vpn_core::{Vpn<PERSON><PERSON><PERSON>, VpnError, Result};
use clap::{Parser, Subcommand};
use std::net::SocketAddr;
use tracing::{info, error};

/// UltraSecure VPN Client CLI
#[derive(Parser)]
#[command(name = "ultrasecure-vpn-client")]
#[command(about = "UltraSecure VPN Client - Beyond Military Grade Security")]
#[command(version = "0.1.0")]
struct Cli {
    #[command(subcommand)]
    command: Commands,
    
    /// Enable debug logging
    #[arg(short, long, global = true)]
    debug: bool,
    
    /// Configuration file
    #[arg(short, long, global = true, default_value = "client.toml")]
    config: String,
}

#[derive(Subcommand)]
enum Commands {
    /// Connect to VPN server
    Connect {
        /// Server address
        #[arg(short, long)]
        server: SocketAddr,
        
        /// License key
        #[arg(short, long)]
        license: String,
        
        /// Local bind address
        #[arg(short, long, default_value = "0.0.0.0:0")]
        bind: SocketAddr,
    },
    
    /// Generate a demo license key
    GenerateLicense {
        /// User ID
        #[arg(short, long)]
        user_id: String,
        
        /// License tier (basic, premium, enterprise)
        #[arg(short, long, default_value = "basic")]
        tier: String,
        
        /// Duration in days
        #[arg(short, long, default_value = "30")]
        days: u64,
    },
    
    /// Show client status
    Status,
    
    /// Test connection to server
    Test {
        /// Server address
        #[arg(short, long)]
        server: SocketAddr,
    },
}

#[tokio::main]
async fn main() -> Result<()> {
    let cli = Cli::parse();
    
    // Initialize logging
    let log_level = if cli.debug { "debug" } else { "info" };
    tracing_subscriber::fmt()
        .with_env_filter(format!("ultrasecure_vpn_client={},ultrasecure_vpn_core={}", log_level, log_level))
        .init();
    
    print_banner();
    
    match cli.command {
        Commands::Connect { server, license, bind } => {
            connect_to_vpn(server, license, bind).await
        }
        Commands::GenerateLicense { user_id, tier, days } => {
            generate_demo_license(user_id, tier, days).await
        }
        Commands::Status => {
            show_status().await
        }
        Commands::Test { server } => {
            test_connection(server).await
        }
    }
}

/// Connect to VPN server
async fn connect_to_vpn(server: SocketAddr, license: String, bind: SocketAddr) -> Result<()> {
    info!("🔗 Connecting to VPN server at {}", server);
    info!("🔑 Using license: {}...", &license[..std::cmp::min(license.len(), 20)]);
    
    // Initialize VPN core
    ultrasecure_vpn_core::init()?;
    
    // Create VPN engine
    let mut engine = VpnEngine::new(bind, server, license).await?;
    
    info!("🚀 VPN engine initialized");
    
    // Handle shutdown gracefully
    let shutdown_signal = async {
        tokio::signal::ctrl_c()
            .await
            .expect("Failed to install CTRL+C signal handler");
        info!("🛑 Shutdown signal received");
    };
    
    // Connect to VPN
    tokio::select! {
        result = engine.connect() => {
            match result {
                Ok(_) => info!("✅ VPN connection established"),
                Err(e) => error!("❌ VPN connection failed: {}", e),
            }
        }
        _ = shutdown_signal => {
            info!("🔄 Disconnecting from VPN...");
        }
    }
    
    info!("👋 VPN client stopped");
    Ok(())
}

/// Generate a demo license key for testing
async fn generate_demo_license(user_id: String, tier: String, days: u64) -> Result<()> {
    use ultrasecure_vpn_core::licensing::{LicenseClaims, get_hardware_fingerprint};
    use std::time::{SystemTime, UNIX_EPOCH};
    use uuid::Uuid;
    
    info!("🎫 Generating demo license for user: {}", user_id);
    
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();
    
    let expires_at = now + (days * 24 * 60 * 60);
    let hardware_fingerprint = get_hardware_fingerprint();
    
    let claims = LicenseClaims {
        license_id: Uuid::new_v4().to_string(),
        user_id: user_id.clone(),
        hardware_fingerprint: hardware_fingerprint.clone(),
        expires_at,
        issued_at: now,
        max_connections: match tier.as_str() {
            "basic" => 1,
            "premium" => 3,
            "enterprise" => 10,
            _ => 1,
        },
        features: vec![
            "encryption".to_string(),
            "obfuscation".to_string(),
            if tier != "basic" { "priority_support".to_string() } else { "".to_string() },
        ].into_iter().filter(|s| !s.is_empty()).collect(),
        tier: tier.clone(),
        bandwidth_limit: match tier.as_str() {
            "basic" => 10_000_000,      // 10 MB/s
            "premium" => 100_000_000,   // 100 MB/s
            "enterprise" => 0,          // Unlimited
            _ => 10_000_000,
        },
        data_limit: match tier.as_str() {
            "basic" => 10_000_000_000,      // 10 GB/month
            "premium" => 100_000_000_000,   // 100 GB/month
            "enterprise" => 0,              // Unlimited
            _ => 10_000_000_000,
        },
        allowed_regions: vec![], // All regions allowed
    };
    
    // Create a simple JWT-like token (for demo purposes)
    let header = base64::encode(r#"{"alg":"HS256","typ":"JWT"}"#);
    let payload = base64::encode(serde_json::to_string(&claims).unwrap());
    let signature = base64::encode("demo_signature"); // In production, use proper signing
    
    let token = format!("{}.{}.{}", header, payload, signature);
    
    println!("\n🎫 Demo License Generated:");
    println!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    println!("👤 User ID: {}", user_id);
    println!("🏷️  Tier: {}", tier);
    println!("⏰ Expires: {} days from now", days);
    println!("🖥️  Hardware: {}...", &hardware_fingerprint[..16]);
    println!("🔗 Max Connections: {}", claims.max_connections);
    println!("📊 Bandwidth Limit: {} MB/s", claims.bandwidth_limit / 1_000_000);
    println!("💾 Data Limit: {} GB/month", claims.data_limit / 1_000_000_000);
    println!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    println!("🔑 License Token:");
    println!("{}", token);
    println!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    println!("\n💡 Usage:");
    println!("   ultrasecure-vpn-client connect --server <SERVER_IP:PORT> --license \"{}\"", token);
    println!("\n⚠️  Note: This is a demo license for testing purposes only.");
    
    Ok(())
}

/// Show client status
async fn show_status() -> Result<()> {
    println!("📊 UltraSecure VPN Client Status");
    println!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    
    // Check if VPN is running
    println!("🔌 Connection Status: Disconnected");
    println!("🖥️  Hardware Fingerprint: {}", ultrasecure_vpn_core::licensing::get_hardware_fingerprint());
    
    // Show network interfaces
    match ultrasecure_vpn_core::network::NetworkInterface::get_interfaces() {
        Ok(interfaces) => {
            println!("🌐 Network Interfaces:");
            for interface in interfaces {
                println!("   • {} - {} ({})", 
                    interface.name, 
                    interface.ip_address,
                    if interface.is_up { "UP" } else { "DOWN" }
                );
            }
        }
        Err(e) => {
            println!("❌ Failed to get network interfaces: {}", e);
        }
    }
    
    // Show default gateway
    match ultrasecure_vpn_core::network::NetworkInterface::get_default_gateway() {
        Ok(gateway) => {
            println!("🚪 Default Gateway: {}", gateway);
        }
        Err(e) => {
            println!("❌ Failed to get default gateway: {}", e);
        }
    }
    
    println!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    
    Ok(())
}

/// Test connection to server
async fn test_connection(server: SocketAddr) -> Result<()> {
    info!("🧪 Testing connection to {}", server);
    
    // Test basic connectivity
    let reachable = ultrasecure_vpn_core::network::NetworkInterface::is_reachable(
        server.ip(),
        server.port(),
        5000, // 5 second timeout
    ).await;
    
    if reachable {
        println!("✅ Server {} is reachable", server);
        
        // Measure latency
        match ultrasecure_vpn_core::network::LatencyMeasurement::measure_rtt(server).await {
            Ok(rtt) => {
                println!("⚡ Round-trip time: {:?}", rtt);
            }
            Err(e) => {
                println!("⚠️  Could not measure latency: {}", e);
            }
        }
    } else {
        println!("❌ Server {} is not reachable", server);
        return Err(VpnError::NetworkError("Server not reachable".to_string()));
    }
    
    Ok(())
}

/// Print application banner
fn print_banner() {
    println!(r#"
╔══════════════════════════════════════════════════════════════╗
║                   UltraSecure VPN Client                    ║
║                                                              ║
║  🔒 Triple-layer encryption (XChaCha20 + AES-256 + Custom)  ║
║  🥷 Advanced traffic obfuscation                             ║
║  🔑 Hardware-bound license authentication                    ║
║  🌐 Cross-platform support                                  ║
║  💰 Budget-friendly deployment                               ║
║                                                              ║
║  Security Level: BEYOND MILITARY GRADE                      ║
╚══════════════════════════════════════════════════════════════╝
    "#);
}
